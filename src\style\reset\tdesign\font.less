
:root {
  // 使用 Figma 设计的字体族定义
  --td-font-family: var(--font-family-font-sans);
  --td-font-family-medium: var(--font-family-font-sans);

  // 基于 Figma 设计的字体大小 token
  --td-font-size-link-small: var(--font-size-test-12);
  --td-font-size-link-medium: var(--font-size-test-14);
  --td-font-size-link-large: var(--font-size-test-16);
  --td-font-size-mark-small: var(--font-size-test-12);
  --td-font-size-mark-medium: var(--font-size-test-14);
  --td-font-size-body-small: var(--font-size-test-12);
  --td-font-size-body-medium: var(--font-size-test-14);
  --td-font-size-body-large: var(--font-size-test-16);
  --td-font-size-title-small: var(--font-size-test-14);
  --td-font-size-title-medium: var(--font-size-test-16);
  --td-font-size-title-large: var(--font-size-test-20);
  --td-font-size-headline-small: var(--font-size-test-24);
  --td-font-size-headline-medium: var(--font-size-test-30);
  --td-font-size-headline-large: var(--font-size-test-36);
  --td-font-size-display-medium: 48px;
  --td-font-size-display-large: 64px;

  // 基于 Figma 设计的行高 token
  --td-line-height-link-small: var(--line-height-test-16);
  --td-line-height-link-medium: var(--line-height-test-18);
  --td-line-height-link-large: var(--line-height-test-20);
  --td-line-height-mark-small: var(--line-height-test-16);
  --td-line-height-mark-medium: var(--line-height-test-18);
  --td-line-height-body-small: var(--line-height-test-16);
  --td-line-height-body-medium: var(--line-height-test-18);
  --td-line-height-body-large: var(--line-height-test-20);
  --td-line-height-title-small: var(--line-height-test-18);
  --td-line-height-title-medium: var(--line-height-test-20);
  --td-line-height-title-large: var(--line-height-test-24);
  --td-line-height-headline-small: var(--line-height-test-28);
  --td-line-height-headline-medium: var(--line-height-test-32);
  --td-line-height-headline-large: var(--line-height-test-40);
  --td-line-height-display-medium: 56px;
  --td-line-height-display-large: 72px;

  // 基于 Figma 设计的字体权重
  --td-font-weight-normal: var(--font-weight-font-normal);
  --td-font-weight-medium: var(--font-weight-font-medium);
  --td-font-weight-semibold: var(--font-weight-font-semibold);
  --td-font-weight-bold: var(--font-weight-font-bold);

  // 字体 token 集合 - 使用 Figma 设计的字体权重
  --td-font-link-small: var(--td-font-size-link-small) / var(--td-line-height-link-small) var(--td-font-family);
  --td-font-link-medium: var(--td-font-size-link-medium) / var(--td-line-height-link-medium) var(--td-font-family);
  --td-font-link-large: var(--td-font-size-link-large) / var(--td-line-height-link-large) var(--td-font-family);
  --td-font-mark-small: var(--td-font-weight-semibold) var(--td-font-size-mark-small) / var(--td-line-height-mark-small) var(--td-font-family);
  --td-font-mark-medium: var(--td-font-weight-semibold) var(--td-font-size-mark-medium) / var(--td-line-height-mark-medium) var(--td-font-family);
  --td-font-body-small: var(--td-font-size-body-small) / var(--td-line-height-body-small) var(--td-font-family);
  --td-font-body-medium: var(--td-font-size-body-medium) / var(--td-line-height-body-medium) var(--td-font-family);
  --td-font-body-large: var(--td-font-size-body-large) / var(--td-line-height-body-large) var(--td-font-family);
  --td-font-title-small: var(--td-font-weight-semibold) var(--td-font-size-title-small) / var(--td-line-height-title-small) var(--td-font-family);
  --td-font-title-medium: var(--td-font-weight-semibold) var(--td-font-size-title-medium) / var(--td-line-height-title-medium) var(--td-font-family);
  --td-font-title-large: var(--td-font-weight-semibold) var(--td-font-size-title-large) / var(--td-line-height-title-large) var(--td-font-family);
  --td-font-headline-small: var(--td-font-weight-semibold) var(--td-font-size-headline-small) / var(--td-line-height-headline-small) var(--td-font-family);
  --td-font-headline-medium: var(--td-font-weight-semibold) var(--td-font-size-headline-medium) / var(--td-line-height-headline-medium) var(--td-font-family);
  --td-font-headline-large: var(--td-font-weight-semibold) var(--td-font-size-headline-large) / var(--td-line-height-headline-large) var(--td-font-family);
  --td-font-display-medium: var(--td-font-weight-semibold) var(--td-font-size-display-medium) / var(--td-line-height-display-medium) var(--td-font-family);
  --td-font-display-large: var(--td-font-weight-semibold) var(--td-font-size-display-large) / var(--td-line-height-display-large) var(--td-font-family);
}