<template>
  <header-sidebar-layout>
    <!-- 顶栏内容 -->
    <template #header>
      <div class="header-content">
        <h2 class="page-title">组件示例</h2>
      </div>
    </template>

    <!-- 左侧导航 -->
    <template #sidebar>
      <div class="sidebar-content">
        <t-menu v-model:value="activeExample" :default-value="activeExample" @change="handleMenuChange">
          <t-menu-group v-for="group in menuItems" :key="group.title" :title="group.title">
            <t-menu-item v-for="item in group.items" :key="item.value" :value="item.value">
              {{ item.label }}
            </t-menu-item>
          </t-menu-group>
        </t-menu>
      </div>
    </template>

    <template #main>
      <div class="main-content">
        <router-view />
      </div>
    </template>
  </header-sidebar-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import HeaderSidebarLayout from '@/layouts/layout-header-sidebar/index.vue';

defineOptions({
  name: 'DevelopIndex'
});

const router = useRouter();

// 当前激活的示例
const activeExample = ref('');

const menuItems = [
  {
    title: '国际化',
    items: [{ value: 'locale', label: 'Locale 语言包' }]
  },
  {
    title: '基础组件',
    items: [
      { value: 'button', label: 'Button 按钮组件' },
      { value: 'input', label: 'Input 输入框组件' },
      { value: 'table', label: 'Table 表格组件' }
    ]
  },
  {
    title: '业务组件',
    items: [
      { value: 'pos-avatar', label: 'PosAvatar 头像组件' },
      { value: 'pos-area-cascader', label: 'PosAreaCascader 省区市选择' },
      { value: 'pos-alert', label: 'PosAlert 弹窗组件' }
    ]
  },
  {
    title: '通信桥',
    items: [{ value: 'native-api', label: 'NativeApi 通信桥' }]
  }
];

// 方法
const handleMenuChange = (value: string) => {
  activeExample.value = value;
  router.push(`/develop/${value}`);
};
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
