<template>
  <div class="account-login">
    <!-- 登录表单 -->
    <form class="login-form" autocomplete="on" @submit.prevent="handleLogin">
      <!-- 手机号输入 -->
      <div class="form-field">
        <label class="field-label">手机号</label>
        <div class="field-input">
          <t-input
            v-model="formData.phone"
            placeholder="请填写管理员手机号"
            size="large"
            :status="errors.phone ? 'error' : 'default'"
            autocomplete="username"
            name="username"
          />
        </div>
      </div>

      <!-- 密码输入 -->
      <div class="form-field">
        <label class="field-label">密码</label>
        <div class="field-input">
          <t-input
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :status="errors.password ? 'error' : 'default'"
            autocomplete="current-password"
            name="password"
          />
        </div>
      </div>

      <!-- 记住密码与忘记密码 -->
      <div class="form-row">
        <t-checkbox v-model="formData.rememberPassword"> 记住密码 </t-checkbox>
        <button type="button" class="forget-password-btn" @click="emit('showForgetPassword')">忘记密码</button>
      </div>

      <!-- 登录按钮 -->
      <t-button theme="primary" size="large" block :loading="isLoading" :disabled="!isFormValid" @click="handleLogin">
        登录
      </t-button>
    </form>
  </div>
</template>

<script setup lang="ts">
/**
 * @file 账号登录组件
 * @description 用户名密码登录组件，包含表单验证和登录逻辑
 */
import { reactive, computed, ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { postPasswordLogin } from '@/api/login';
import { useUserStore } from '@/store/modules/user';

// 定义事件
const emit = defineEmits<{
  (_e: 'showForgetPassword'): void;
}>();

const router = useRouter();
const userStore = useUserStore();

// 表单数据
const formData = reactive({
  phone: '',
  password: '',
  rememberPassword: false
});

// 表单验证错误
const errors = reactive({
  phone: '',
  password: ''
});

// 加载状态
const isLoading = ref(false);

// 表单验证
const isFormValid = computed(() => {
  return formData.phone.trim() !== '' && formData.password.trim() !== '';
});

// 加载记住的密码信息
const loadRememberInfo = () => {
  const rememberInfo = userStore.getRememberInfo();
  if (rememberInfo && rememberInfo.rememberPassword) {
    formData.phone = rememberInfo.phone || '';
    formData.password = rememberInfo.password || '';
    formData.rememberPassword = true;
  }
};

// 登录处理
const handleLogin = async () => {
  if (!isFormValid.value) return;

  isLoading.value = true;
  try {
    // 调用登录API
    const response = await postPasswordLogin({
      account: formData.phone,
      password: formData.password
    });

    // 保存登录token
    userStore.setToken(response.data.token);

    // 处理记住密码逻辑
    userStore.saveRememberInfo(formData.phone, formData.password, formData.rememberPassword);

    // 跳转到主页
    router.push('/');
  } catch (error) {
    console.error('登录失败:', error);
    // 这里可以添加错误提示逻辑
  } finally {
    isLoading.value = false;
  }
};

// 监听记住密码复选框状态变化
watch(
  () => formData.rememberPassword,
  newValue => {
    // 当取消勾选记住密码时，立即清空保存的信息
    if (!newValue) {
      userStore.clearRememberInfo();
    }
  }
);

// 组件初始化时加载记住的信息
onMounted(() => {
  loadRememberInfo();
});

defineOptions({
  name: 'AccountLogin'
});
</script>

<style scoped lang="less">
.account-login {
  width: 100%;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.field-label {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: var(--text-primary);
}

.field-input {
  display: flex;
  width: 100%;
}

.form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 20px;
}

.forget-password-btn {
  border: none;
  background: transparent;
  padding: 0;
  cursor: pointer;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: var(--text-brand-tertiary);
  transition: color 0.2s ease;

  &:hover {
    color: var(--text-brand-secondary-hover);
  }
}

:deep(.t-input__inner) {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.375;

  &::placeholder {
    color: var(--text-secondary);
  }
}

:deep(.t-checkbox__label) {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: var(--text-primary);
}

:deep(.t-button) {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
}
</style>
