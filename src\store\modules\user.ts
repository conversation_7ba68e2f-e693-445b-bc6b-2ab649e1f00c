import { defineStore } from 'pinia';
import storage from '@/utils/store-secure';

// 使用 pinia 的 defineStore 方法定义一个全局状态管理器
export const useUserStore = defineStore('user', {
  // 定义状态对象，这里包含三个状态
  state: () => ({
    token: '', // 登录令牌
    historyUser: [] as { id: string; name: string }[], // 历史用户
    permission: [] as string[] // 权限
  }),
  // 定义操作状态的方法
  actions: {
    // 设置登录令牌
    setToken(token: string) {
      this.token = token;
    },
    // 添加历史用户
    setHistoryUser(user: { id: string; name: string }) {
      this.historyUser.push(user);
    },
    // 设置权限
    setPermission(permission: string[]) {
      this.permission = permission;
    }
  },
  // 持久化配置
  persist: {
    storage,
    pick: ['token', 'historyUser', 'permission'] // 持久化目录
  }
});
