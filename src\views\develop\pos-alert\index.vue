<template>
  <div class="pos-alert-demo">
    <t-button @click="showWarningAlert">Warning Alert</t-button>
    <t-button @click="showSuccessAlert">Success Alert</t-button>

    <pos-alert
      v-model:visible="warningVisible"
      theme="danger"
      title="删除账号"
      content="删除后不可恢复，确定要删除已绑定的【蜜雪冰城甜蜜蜜】吗？"
      confirm-btn="删除"
      @confirm="onConfirm"
      @cancel="onCancel"
    />

    <pos-alert
      v-model:visible="successVisible"
      theme="success"
      title="Account Deleted"
      content="Your account has been successfully deleted."
      confirm-btn="OK"
      @confirm="onConfirm"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import PosAlert from '@/components/bussiness/pos-alert/index.vue';

const warningVisible = ref(false);
const successVisible = ref(false);

const showWarningAlert = () => {
  warningVisible.value = true;
};

const showSuccessAlert = () => {
  successVisible.value = true;
};

const onConfirm = () => {
  console.log('Confirmed');
};

const onCancel = () => {
  console.log('Cancelled');
};
</script>

<style lang="less" scoped>
.pos-alert-demo {
  padding: 24px;
  display: flex;
  gap: 16px;
}
</style>
