import { tracking, customReport, getReports } from 'frontend-tracking';
import type { App } from 'vue';
import { CreateNativeApi } from '@/bridge';
import { date } from 'frontend-utils';
import { useConfigStore } from '@/store';

/**
 * @description 初始化应用追踪与日志上报功能
 * @param app Vue 应用实例
 */
export const createTracking = (app: App) => {
  // 初始化前端追踪SDK
  tracking({
    appId: 'pos', // 应用的唯一标识
    locally: true, // 是否将日志数据存储在本地
    maxSize: 100000, // 本地存储的最大日志条数
    expire: 2592000000, // 本地日志的过期时间 (毫秒, 默认30天)
    hash: false, // 是否监听 URL hash 变化
    reportType: [
      { type: 'js-error' }, // 自动上报 JavaScript 运行时错误
      { type: 'action' }, // 自动上报用户交互事件 (如点击)
      { type: 'custom' } // 允许上报自定义类型的报告
    ]
  });

  /**
   * @description Vue 全局异常处理器
   * 当 Vue 应用内部抛出未捕获的异常时，此函数将被调用。
   */
  app.config.errorHandler = (err: unknown, _instance, info) => {
    // 提取错误堆栈信息
    const errorStack = err instanceof Error ? err.stack : String(err);
    // 上报 Vue 相关的特定错误
    customReport({
      customType: 'vue-error', // 自定义错误类型
      stack: errorStack, // 错误堆栈
      info // Vue 提供的额外调试信息
    });
    // 在控制台打印错误，便于开发调试
    console.error(errorStack);
  };

  const nativeApi = CreateNativeApi();

  // 设置日志定时上报的时间间隔，单位为毫秒 (1小时)
  const reportInterval = 60 * 60 * 1000;

  /**
   * @description 启动定时器，周期性地将本地存储的日志上报到服务端
   */
  setInterval(() => {
    const configStore = useConfigStore();
    const lastReportTime = configStore.lastReportTime;

    let startTime: string;

    const now = date();
    // 本次上报的结束时间点
    const endTime = now.format('YYYY/MM/DD HH:mm:ss');

    if (!lastReportTime) {
      // 如果从未上报过，则上报从今天零点到当前时间的日志
      startTime = now.startOf('day').format('YYYY/MM/DD HH:mm:ss');
    } else {
      // 否则，上报从上次成功上报的时间点到当前时间的日志
      startTime = lastReportTime;
    }

    // 根据时间范围从本地获取待上报的日志
    getReports(startTime, endTime).then((reports: Array<object>) => {
      if (!reports || reports.length === 0) {
        // 如果没有日志需要上报，则直接返回
        return;
      }
      // 通过原生桥接调用 native 的日志记录接口
      nativeApi.request['logger.log']({ context: reports }).then(res => {
        if (res.success) {
          // 如果上报成功，更新本地存储的最后上报时间
          configStore.setLastReportTime(endTime);
        } else {
          // 如果上报失败，记录一条上报失败的日志，以便后续排查
          customReport({
            customType: 'log-error',
            time: endTime
          });
        }
      });
    });
  }, reportInterval);
};
