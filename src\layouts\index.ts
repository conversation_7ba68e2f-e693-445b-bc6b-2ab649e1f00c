// 全屏布局组件
export { default as LayoutFullscreen } from './layout-full-screen/index.vue';

// 左侧边栏布局组件
export { default as LayoutLeftSidebar } from './layout-left-sidebar/index.vue';

// 右侧边栏布局组件
export { default as LayoutRightSidebar } from './layout-right-sidebar/index.vue';

// 顶部带侧边栏布局组件
export { default as LayoutHeaderSidebar } from './layout-header-sidebar/index.vue';

// 左右并排布局组件
export { default as LayoutSideBySide } from './layout-side-by-side/index.vue';

// 上下布局组件
export { default as LayoutTopDown } from './layout-top-down/index.vue';
