/**
 * 文档组件模板样式
 * 提取自 locale 组件的通用样式，供其他文档组件复用
 */

// 文档页面容器
.doc-demo {
  padding: var(--td-comp-paddingLR-xl);
  background: var(--td-bg-color-page);
  min-height: 100vh;
}

// 文档卡片容器
.doc-card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-default);
  padding: var(--td-comp-paddingLR-xl);
  box-shadow: var(--td-shadow-1, 0 2px 8px rgba(0, 0, 0, 0.1));
  transition: all 0.2s ease;

  h3 {
    margin-bottom: var(--td-comp-margin-xl);
    color: var(--td-text-color-primary);
    font-size: 20px;
    font-weight: 500;
    padding-bottom: var(--td-comp-paddingTB-m);
  }
}

// 功能切换区域
.doc-switch {
  margin-bottom: var(--td-comp-margin-xxxxl);
  padding: var(--td-comp-paddingLR-l);
  background: var(--td-bg-color-secondarycontainer);
  border-radius: var(--td-radius-medium);
  transition: background-color 0.2s ease;

  h4 {
    margin-bottom: var(--td-comp-margin-m);
    color: var(--td-text-color-primary);
    font-size: 16px;
    font-weight: 500;
  }
}

// 文档章节
.doc-section {
  margin-bottom: var(--td-comp-margin-xxxxl);

  h4 {
    margin-bottom: var(--td-comp-margin-l);
    color: var(--td-text-color-primary);
    font-size: 16px;
    font-weight: 500;
    position: relative;
    padding-left: var(--td-comp-paddingLR-s);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background: var(--td-brand-color);
      border-radius: var(--td-radius-small);
    }
  }

  h5 {
    margin-bottom: var(--td-comp-margin-s);
    color: var(--td-text-color-secondary);
    font-size: 14px;
    font-weight: 500;
  }
}

// 示例展示项
.doc-item {
  padding: var(--td-comp-paddingLR-l);
  border-radius: var(--td-radius-medium);
  background: var(--td-bg-color-secondarycontainer);
  transition: all 0.2s ease;

  .label {
    display: inline-block;
    margin-right: var(--td-comp-margin-m);
    color: var(--td-text-color-secondary);
    font-size: 14px;
    font-weight: 500;
  }

  :deep(.t-button) {
    margin-right: var(--td-comp-margin-s);
    margin-bottom: var(--td-comp-margin-xs);
  }

  :deep(.t-form-item) {
    margin-bottom: var(--td-comp-margin-l);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 信息展示区域
.doc-info {
  p {
    margin: var(--td-comp-margin-s) 0;
    color: var(--td-text-color-secondary);
    font-size: 14px;
    line-height: 1.5;
    transition: color 0.2s ease;
  }
}

// 代码示例区域
.doc-code {
  padding: var(--td-comp-paddingLR-l);
  background: var(--td-bg-color-secondarycontainer);
  border-radius: var(--td-radius-medium);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;

  pre {
    margin: 0;
    margin-top: var(--td-comp-margin-s);
    font-size: 14px;
    line-height: 1.6;
    color: var(--td-text-color-primary);
    white-space: pre-wrap;
    word-wrap: break-word;
    background: transparent;

    code {
      background: transparent;
      padding: 0;
      border-radius: 0;
      color: inherit;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
    }
  }
}

// 高亮代码样式
.hljs {
  background: transparent !important;
  padding: 0 !important;
}

// 代码语法高亮主题（适配 TDesign 变量）
.doc-code pre {
  // 关键字
  .hljs-keyword,
  .hljs-built_in,
  .hljs-type {
    color: var(--td-brand-color);
    font-weight: 600;
  }

  // 字符串
  .hljs-string,
  .hljs-template-string {
    color: var(--td-success-color);
  }

  // 注释
  .hljs-comment,
  .hljs-quote {
    color: var(--td-text-color-placeholder);
    font-style: italic;
  }

  // 数字
  .hljs-number,
  .hljs-literal {
    color: var(--td-warning-color);
    font-weight: 500;
  }

  // 属性
  .hljs-attr,
  .hljs-attribute {
    color: var(--td-brand-color-8);
    font-weight: 500;
  }

  // 函数名
  .hljs-function,
  .hljs-title {
    color: var(--td-brand-color-6);
    font-weight: 500;
  }

  // 变量
  .hljs-variable,
  .hljs-name {
    color: var(--td-error-color);
  }

  // 标签
  .hljs-tag {
    color: var(--td-brand-color);
  }

  // 运算符
  .hljs-operator,
  .hljs-punctuation {
    color: var(--td-text-color-primary);
  }

  // 类名
  .hljs-class,
  .hljs-title.class_ {
    color: var(--td-warning-color-6);
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .doc-demo {
    padding: var(--td-comp-paddingLR-l);
  }

  .doc-card {
    padding: var(--td-comp-paddingLR-l);
  }

  .doc-switch {
    padding: var(--td-comp-paddingLR-m);
  }

  .doc-item {
    padding: var(--td-comp-paddingLR-m);

    .label {
      display: block;
      margin-bottom: var(--td-comp-margin-xs);
      margin-right: 0;
    }
  }

  .doc-code {
    padding: var(--td-comp-paddingLR-m);

    pre {
      font-size: 12px;
    }
  }
}