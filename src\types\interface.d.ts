// API 响应基础类型
interface ApiResponse<T = any> {
  data: T;
  msg?: string;
  code?: number;
  status?: number;
}

interface Window {
  chrome?: {
    webview?: {
      postMessage: (msg: any) => void;
      addEventListener: (type: string, handler: (event: { data: any }) => void) => void;
    };
  };
  AndroidBridge?: { postMessage: (msg: string) => void };
  onNativeMessage?: (msg: string) => void;
}

interface AppConfig {
  errorHandler?: (
    err: unknown,
    instance: ComponentPublicInstance | null,
    // `info` 是一个 Vue 特定的错误信息
    // 例如：错误是在哪个生命周期的钩子上抛出的
    info: string
  ) => void
}

// 声明 .md 文件的模块类型
declare module '*.md' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}