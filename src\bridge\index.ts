import BridgeService from './bridge';
import type { NativeApi } from '@/bridge/types';

let nativeApi: NativeApi | null = null;

export const CreateNativeApi = () => {
  if (nativeApi) {
    return nativeApi;
  }
  const bridge = new BridgeService();

  /**
   * 完全类型安全的 Native API 对象
   * 按照三种通信模式组织：request、notify、on
   */
  nativeApi = {
    /**
     * 模式1: Web 请求原生，需要有返回值
     * 使用 bridge.invoke 方法，返回 Promise
     */
    request: {
      'printer.print': params => bridge.invoke('printer.print', params),
      'logger.log': params => bridge.invoke('logger.log', params),
      'file.save': params => bridge.invoke('file.save', params)
    },

    /**
     * 模式2: Web 通知原生，不需要有返回值
     * 使用 bridge.notify 方法，无返回值
     */
    notify: {
      'ui.showToast': params => bridge.notify('ui.showToast', params),
      'ui.hideLoading': params => bridge.notify('ui.hideLoading', params),
      'ui.setTitle': params => bridge.notify('ui.setTitle', params),
      'tracking.event': params => bridge.notify('tracking.event', params)
    },

    /**
     * 模式3: 监听原生事件，不需要有返回值
     * 使用 bridge.subscribe 方法，返回取消订阅函数
     */
    on: {
      'scanner.dataReceived': listener => bridge.subscribe('scanner.dataReceived', listener),
      'network.statusChanged': listener => bridge.subscribe('network.statusChanged', listener),
      'device.batteryChanged': listener => bridge.subscribe('device.batteryChanged', listener),
      'app.visibilityChanged': listener => bridge.subscribe('app.visibilityChanged', listener)
    }
  };

  return nativeApi;
};

export default CreateNativeApi;
