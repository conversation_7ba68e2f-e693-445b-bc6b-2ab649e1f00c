import BridgeService from './bridge';
import type { NativeApi } from '@/bridge/types';

let nativeApi: NativeApi | null = null;

export const CreateNativeApi = () => {
  if (nativeApi) {
    return nativeApi;
  }
  const bridge = new BridgeService();

  /**
   * 完全类型安全的 Native API 对象
   * 按照三种通信模式组织：request、notify、on / off
   */
  nativeApi = {
    /**
     * 模式1: Web 请求原生，需要有返回值
     * 使用 bridge.invoke 方法，返回 Promise
     */
    request: {
      'printer.print': params => bridge.invoke('printer.print', params),
      'logger.log': params => bridge.invoke('logger.log', params)
    },

    /**
     * 模式2: Web 通知原生，内置错误处理
     * 使用 bridge.notify 方法，返回成功状态和错误信息
     */
    notify: {
      'screen.update': params => bridge.notify('screen.update', params)
    },

    /**
     * 模式3: 监听原生事件，不需要有返回值
     * 使用 bridge.subscribe 方法，返回取消订阅函数
     */
    on: {
      'screen.dataReceived': listener => bridge.subscribe('screen.dataReceived', listener)
    },

    /**
     * 移除事件监听器
     * 使用 bridge.off 方法，移除指定监听器或所有监听器
     */
    off: {
      'screen.dataReceived': listener => bridge.off('screen.dataReceived', listener)
    }
  };

  return nativeApi;
};

export default CreateNativeApi;
