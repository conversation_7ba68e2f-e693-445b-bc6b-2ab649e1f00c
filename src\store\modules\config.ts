import { defineStore } from 'pinia';
import storage from '@/utils/store-secure';

// 使用 pinia 的 defineStore 方法定义一个全局状态管理器
export const useConfigStore = defineStore('config', {
  // 定义状态对象，这里包含三个状态
  state: () => ({
    isLoading: false, // 加载状态标识，默认为 false
    keepAliveList: [] as string[], // 存放需要进行 keep-alive 缓存的路由名称列表，初始为空
    locale: 'zh_cn', // 存放当前语言，初始为 zh_cn, en
    lastReportTime: '', // 上一次上报时间
    theme: 'light' as 'light' | 'dark', // 主题，初始为 light
    isNewLayout: true // 是否使用新布局，初始为 true
  }),
  // 定义操作状态的方法
  actions: {
    // 设置 isLoading 状态
    setIsLoading(isLoading: boolean) {
      this.isLoading = isLoading;
    },
    // 设置需要进行 keep-alive 缓存的路由名称列表
    setKeepAliveList(keepAliveList: string[]) {
      this.keepAliveList = keepAliveList;
    },
    setLocale(locale: 'zh_cn' | 'en') {
      this.locale = locale;
    },
    setLastReportTime(lastReportTime: string) {
      this.lastReportTime = lastReportTime;
    },
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme;
    },
    setIsNewLayout(isNewLayout: boolean) {
      this.isNewLayout = isNewLayout;
    }
  },
  // 持久化配置
  persist: {
    storage,
    pick: ['locale', 'lastReportTime'] // 持久化目录
  }
});
