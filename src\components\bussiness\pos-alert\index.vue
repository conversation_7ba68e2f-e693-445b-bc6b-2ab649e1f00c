<template>
  <t-dialog
    v-model:visible="visible"
    :close-btn="false"
    :header="false"
    width="400px"
    :footer="false"
    placement="center"
    @confirm="onConfirm"
    @cancel="onCancel"
    @close="onClose"
  >
    <div class="pos-alert">
      <div class="pos-alert__icon">
        <component :is="iconComponent" />
      </div>
      <div class="pos-alert__title">{{ title }}</div>
      <div class="pos-alert__content">{{ content }}</div>
      <div class="pos-alert__actions">
        <t-button theme="default" size="large" @click="onCancel">{{ cancelBtn }}</t-button>
        <t-button :theme="theme" size="large" @click="onConfirm">{{ confirmBtn }}</t-button>
      </div>
    </div>
  </t-dialog>
</template>

<script lang="ts" setup>
import { computed, toRefs } from 'vue';
import { props as componentProps } from './props';
import SuccessIcon from '@/assets/icon_success.svg?component';
import WarningIcon from '@/assets/icon_alert.svg?component';

const emit = defineEmits(['update:visible', 'confirm', 'cancel', 'close']);

const props = defineProps(componentProps);

const { visible, title, content, confirmBtn, cancelBtn, icon, theme } = toRefs(props);

const iconComponent = computed(() => {
  if (icon.value) {
    // This assumes the icon prop is a component or a string that can be resolved to one.
    // For this implementation, we'll focus on the default icons.
    return null;
  }
  return theme.value === 'success' ? SuccessIcon : WarningIcon;
});

const onConfirm = () => {
  emit('confirm');
  emit('update:visible', false);
};

const onCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};

const onClose = () => {
  emit('close');
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.pos-alert {
  display: flex;
  flex-direction: column;
  align-items: center;

  &__icon {
    width: 48px;
    height: 48px;
    margin-bottom: 20px;
  }

  &__title {
    font-size: 20px;
    font-weight: 700;
    line-height: 30px;
    color: var(--td-text-color-primary);
    margin-bottom: 8px;
  }

  &__content {
    font-size: 16px;
    line-height: 22px;
    color: var(--td-text-color-secondary);
    margin-bottom: 32px;
    text-align: center;
  }

  &__actions {
    display: flex;
    gap: 12px;
    width: 100%;

    .t-button {
      flex: 1;
    }
  }
}
</style>
