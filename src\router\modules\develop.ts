export const developRoutes = [
  {
    path: '/develop',
    name: 'DevelopIndex',
    meta: { public: true },
    component: () => import('@/views/develop/index.vue'),

    children: [
      {
        path: 'button',
        name: 'But<PERSON><PERSON>xa<PERSON>',
        meta: { public: true },
        component: () => import('@/views/develop/button/index.vue')
      },
      {
        path: 'table',
        name: 'TableExample',
        meta: { public: true },
        component: () => import('@/views/develop/table/index.vue')
      },
      {
        path: 'locale',
        name: 'LocaleExample',
        meta: { public: true },
        component: () => import('@/views/develop/locale/index.vue')
      }
    ]
  }
];
