export const developRoutes = [
  {
    path: '/develop',
    name: 'DevelopIndex',
    meta: { public: true },
    component: () => import('@/views/develop/index.vue'),

    children: [
      {
        path: 'button',
        name: 'Button<PERSON>xa<PERSON>',
        meta: { public: true },
        component: () => import('@/views/develop/button/index.vue')
      },
      {
        path: 'input',
        name: 'InputExample',
        meta: { public: true },
        component: () => import('@/views/develop/input/index.vue')
      },
      {
        path: 'pos-avatar',
        name: 'PosAvatarExample',
        component: () => import('@/views/develop/pos-avatar/index.vue')
      },
      {
        path: 'pos-area-cascader',
        name: 'PosAreaCascaderExample',
        meta: { public: true },
        component: () => import('@/views/develop/pos-area-cascader/index.vue')
      },
      {
        path: 'table',
        name: 'TableExample',
        meta: { public: true },
        component: () => import('@/views/develop/table/index.vue')
      },
      {
        path: 'locale',
        name: 'LocaleExample',
        meta: { public: true },
        component: () => import('@/views/develop/locale/index.vue')
      },
      {
        path: 'pos-alert',
        name: 'PosAlertExample',
        meta: { public: true },
        component: () => import('@/views/develop/pos-alert/index.vue')
      },
      {
        path: 'native-api',
        name: 'NativeApiExample',
        meta: { public: true },
        component: () => import('@/views/develop/native-api/index.vue')
      }
    ]
  }
];
