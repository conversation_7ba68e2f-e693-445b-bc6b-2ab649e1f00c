:root[theme-mode="dark"] {
  --bg-primary: var(--gray-dark-950);
  --bg-primary_alt: var(--gray-dark-900);
  --bg-primary_hover: var(--gray-dark-800);
  --bg-primary_solid: var(--gray-dark-900);
  --bg-secondary: var(--gray-dark-900);
  --bg-secondary_alt: var(--gray-dark-950);
  --bg-secondary_hover: var(--gray-dark-800);
  --bg-secondary_subtle: var(--gray-dark-900);
  --bg-secondary-solid: var(--gray-dark-600);
  --bg-tertiary: var(--gray-dark-800);
  --bg-quaternary: var(--gray-dark-700);
  --bg-active: var(--gray-dark-800);
  --bg-disabled: var(--gray-dark-800);
  --bg-disabled_subtle: var(--gray-dark-900);
  --bg-overlay: var(--basic-overlay-dark-mode);
  --bg-brand-primary: var(--brand-500);
  --bg-brand-primary_alt: var(--gray-dark-800);
  --bg-brand-secondary: var(--brand-600);
  --bg-brand-solid: var(--brand-600);
  --bg-brand-solid_hover: var(--brand-500);
  --bg-brand-section: var(--gray-dark-800);
  --bg-brand-section_subtle: var(--gray-dark-950);
  --bg-error-primary: var(--error-950);
  --bg-error-secondary: var(--error-600);
  --bg-error-solid: var(--error-600);
  --bg-error-solid_hover: var(--error-500);
  --bg-warning-primary: var(--warning-950);
  --bg-warning-secondary: var(--warning-600);
  --bg-warning-solid: var(--warning-600);
  --bg-success-primary: var(--success-950);
  --bg-success-secondary: var(--success-600);
  --bg-success-solid: var(--success-600);
  --fg-primary: var(--basic-white);
  --fg-secondary: var(--gray-dark-300);
  --fg-secondary_hover: var(--gray-dark-200);
  --fg-tertiary: var(--gray-dark-400);
  --fg-tertiary_hover: var(--gray-dark-300);
  --fg-quaternary: var(--gray-dark-600);
  --fg-quaternary_hover: var(--gray-dark-500);
  --fg-white: var(--basic-white);
  --fg-disabled: var(--gray-dark-500);
  --fg-disabled_subtle: var(--gray-dark-600);
  --fg-brand-primary: var(--brand-500);
  --fg-brand-primary_alt: var(--gray-dark-300);
  --fg-brand-secondary: var(--brand-500);
  --fg-brand-secondary_alt: var(--gray-dark-600);
  --fg-error-primary: var(--error-500);
  --fg-error-secondary: var(--error-400);
  --fg-warning-primary: var(--warning-500);
  --fg-warning-secondary: var(--warning-400);
  --fg-success-primary: var(--success-500);
  --fg-success-secondary: var(--success-400);
  --border-primary: var(--gray-dark-700);
  --border-secondary: var(--gray-dark-800);
  --border-secondary_alt: var(--gray-dark-800);
  --border-tertiary: var(--gray-dark-800);
  --border-disabled: var(--gray-dark-700);
  --border-disabled_subtle: var(--gray-dark-800);
  --border-brand: var(--brand-400);
  --border-error: var(--error-400);
  --border-error_subtle: var(--error-500);
  --text-primary: var(--gray-dark-50);
  --text-primary_on-brand: var(--gray-dark-50);
  --text-secondary: var(--gray-dark-300);
  --text-secondary-hover: var(--gray-dark-200);
  --text-secondary_on-brand: var(--gray-dark-300);
  --text-tertiary: var(--gray-dark-400);
  --text-tertiary_hover: var(--gray-dark-300);
  --text-tertiary_on-brand: var(--gray-dark-400);
  --text-quaternary: var(--gray-dark-400);
  --text-quaternary_on-brand: var(--gray-dark-400);
  --text-white: var(--basic-white);
  --text-disabled: var(--gray-dark-500);
  --text-placeholder: var(--gray-dark-700);
  --text-brand-primary: var(--gray-dark-50);
  --text-brand-secondary: var(--gray-dark-300);
  --text-brand-secondary-hover: var(--gray-dark-300);
  --text-brand-tertiary: var(--gray-dark-400);
  --text-error-primary: var(--error-400);
  --text-error-primary_hover: var(--error-500);
  --text-warning-primary: var(--warning-400);
  --text-success-primary: var(--success-400);
  --tag-gray-50: var(--gray-light-900);
  --tag-gray-100: var(--gray-light-800);
  --tag-gray-200: var(--gray-light-700);
  --tag-gray-300: var(--gray-light-700);
  --tag-gray-400: var(--gray-light-600);
  --tag-gray-500: var(--gray-light-500);
  --tag-gray-600: var(--gray-light-400);
  --tag-gray-700: var(--gray-light-300);
  --tag-gray-800: var(--gray-light-200);
  --tag-gray-900: var(--gray-light-100);
  --tag-brand-50: var(--brand-900);
  --tag-brand-100: var(--brand-800);
  --tag-brand-200: var(--brand-700);
  --tag-brand-300: var(--brand-700);
  --tag-brand-400: var(--brand-600);
  --tag-brand-500: var(--brand-500);
  --tag-brand-600: var(--brand-400);
  --tag-brand-700: var(--brand-300);
  --tag-brand-800: var(--brand-200);
  --tag-brand-900: var(--brand-100);
  --tag-error-50: var(--error-950);
  --tag-error-100: var(--error-900);
  --tag-error-200: var(--error-800);
  --tag-error-300: var(--error-700);
  --tag-error-400: var(--error-600);
  --tag-error-500: var(--error-500);
  --tag-error-600: var(--error-400);
  --tag-error-700: var(--error-300);
  --tag-warning-50: var(--warning-950);
  --tag-warning-100: var(--warning-900);
  --tag-warning-200: var(--warning-800);
  --tag-warning-300: var(--warning-700);
  --tag-warning-400: var(--warning-600);
  --tag-warning-500: var(--warning-500);
  --tag-warning-600: var(--warning-400);
  --tag-warning-700: var(--warning-300);
  --tag-success-50: var(--success-950);
  --tag-success-100: var(--success-900);
  --tag-success-200: var(--success-800);
  --tag-success-300: var(--success-700);
  --tag-success-400: var(--success-600);
  --tag-success-500: var(--success-500);
  --tag-success-600: var(--success-400);
  --tag-success-700: var(--success-300);
  --tag-purple-50: var(--purple-950);
  --tag-purple-100: var(--purple-900);
  --tag-purple-200: var(--purple-800);
  --tag-purple-300: var(--purple-700);
  --tag-purple-400: var(--purple-600);
  --tag-purple-500: var(--purple-500);
  --tag-purple-600: var(--purple-400);
  --tag-purple-700: var(--purple-300);
  --tag-blue-50: var(--blue-light-950);
  --tag-blue-100: var(--blue-light-900);
  --tag-blue-200: var(--blue-light-800);
  --tag-blue-300: var(--blue-light-700);
  --tag-blue-400: var(--blue-light-600);
  --tag-blue-500: var(--blue-light-500);
  --tag-blue-600: var(--blue-light-400);
  --tag-blue-700: var(--blue-light-300);
  --tag-indigo-50: var(--indigo-950);
  --tag-indigo-100: var(--indigo-900);
  --tag-indigo-200: var(--indigo-800);
  --tag-indigo-300: var(--indigo-700);
  --tag-indigo-400: var(--indigo-600);
  --tag-indigo-500: var(--indigo-500);
  --tag-indigo-600: var(--indigo-400);
  --tag-indigo-700: var(--indigo-300);
  --tag-orange-50: var(--orange-950);
  --tag-orange-100: var(--orange-900);
  --tag-orange-200: var(--orange-800);
  --tag-orange-300: var(--orange-700);
  --tag-orange-400: var(--orange-600);
  --tag-orange-500: var(--orange-500);
  --tag-orange-600: var(--orange-400);
  --tag-orange-700: var(--orange-300);
  --shadow-1-8: #1018281A;
  --shadow-1-3: #1018281A;
  --shadow-1-offsetx-1: 0px;
  --shadow-1-offsety-1: 12px;
  --shadow-1-blur-1: 16px;
  --shadow-1-spread-1: -4px;
  --shadow-1-offsetx-2: 0px;
  --shadow-1-offsety-2: 4px;
  --shadow-1-blur-2: 6px;
  --shadow-1-spread-2: -2px;
}