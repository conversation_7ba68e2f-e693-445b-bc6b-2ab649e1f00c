:root[theme-mode="dark"] {
  --td-brand-color-1: #0d1926;
  --td-brand-color-2: #111f33;
  --td-brand-color-3: #152540;
  --td-brand-color-4: #1a2b4d;
  --td-brand-color-5: #1e315a;
  --td-brand-color-6: #233766;
  --td-brand-color-7: #0055ff;
  --td-brand-color-8: #3373ff;
  --td-brand-color-9: #6690ff;
  --td-brand-color-10: #99adff;

  --td-warning-color-1: #4f2a1d;
  --td-warning-color-2: #582f21;
  --td-warning-color-3: #733c23;
  --td-warning-color-4: #a75d2b;
  --td-warning-color-5: #cf6e2d;
  --td-warning-color-6: #dc7633;
  --td-warning-color-7: #e8935c;
  --td-warning-color-8: #ecbf91;
  --td-warning-color-9: #eed7bf;
  --td-warning-color-10: #f3e9dc;
  
  --td-error-color-1: #472324;
  --td-error-color-2: #5e2a2d;
  --td-error-color-3: #703439;
  --td-error-color-4: #83383e;
  --td-error-color-5: #a03f46;
  --td-error-color-6: #c64751;
  --td-error-color-7: #de6670;
  --td-error-color-8: #ec888e;
  --td-error-color-9: #edb1b6;
  --td-error-color-10: #eeced0;

  --td-success-color-1: #193a2a;
  --td-success-color-2: #1a4230;
  --td-success-color-3: #17533d;
  --td-success-color-4: #0d7a55;
  --td-success-color-5: #059465;
  --td-success-color-6: #43af8a;
  --td-success-color-7: #46bf96;
  --td-success-color-8: #80d2b6;
  --td-success-color-9: #b4e1d3;
  --td-success-color-10: #deede8;
  
  --td-gray-color-1: #f3f3f3;
  --td-gray-color-2: #eee;
  --td-gray-color-3: #e8e8e8;
  --td-gray-color-4: #ddd;
  --td-gray-color-5: #c6c6c6;
  --td-gray-color-6: #a6a6a6;
  --td-gray-color-7: #8b8b8b;
  --td-gray-color-8: #777;
  --td-gray-color-9: #5e5e5e;
  --td-gray-color-10: #4b4b4b;
  --td-gray-color-11: #393939;
  --td-gray-color-12: #2c2c2c;
  --td-gray-color-13: #242424;
  --td-gray-color-14: #181818;

  // 文字 & 图标 颜色
  --td-font-white-1: rgba(255, 255, 255, 90%);
  --td-font-white-2: rgba(255, 255, 255, 55%);
  --td-font-white-3: rgba(255, 255, 255, 35%);
  --td-font-white-4: rgba(255, 255, 255, 22%);
  --td-font-gray-1: rgba(0, 0, 0, 90%);
  --td-font-gray-2: rgba(0, 0, 0, 60%);
  --td-font-gray-3: rgba(0, 0, 0, 40%);
  --td-font-gray-4: rgba(0, 0, 0, 26%);

  // 基础颜色
  --td-brand-color: var(--td-brand-color-8);     // 色彩-品牌-可操作
  --td-warning-color: var(--td-warning-color-5); // 色彩-功能-警告
  --td-error-color: var(--td-error-color-6);     // 色彩-功能-失败
  --td-success-color: var(--td-success-color-5); // 色彩-功能-成功

  // 基础颜色的扩展 用于 hover / 聚焦 / 禁用 / 点击 等状态
  --td-brand-color-hover: var(--td-brand-color-7);     // hover态
  --td-brand-color-focus: var(--td-brand-color-2);     // focus态，包括鼠标和键盘
  --td-brand-color-active: var(--td-brand-color-9);    // 点击态
  --td-brand-color-disabled: var(--td-brand-color-3);  // 禁用态
  --td-brand-color-light: var(--td-brand-color-1);     // 浅色的选中态
  --td-brand-color-light-hover: var(--td-brand-color-2);

  // 警告色扩展
  --td-warning-color-hover: var(--td-warning-color-4);
  --td-warning-color-focus: var(--td-warning-color-2);
  --td-warning-color-active: var(--td-warning-color-6);
  --td-warning-color-disabled: var(--td-warning-color-3);
  --td-warning-color-light: var(--td-warning-color-1);
  --td-warning-color-light-hover: var(--td-warning-color-2);

  // 失败/错误色扩展
  --td-error-color-hover: var(--td-error-color-5);
  --td-error-color-focus: var(--td-error-color-2);
  --td-error-color-active: var(--td-error-color-7);
  --td-error-color-disabled: var(--td-error-color-3);
  --td-error-color-light: var(--td-error-color-1);
  --td-error-color-light-hover: var(--td-error-color-2);

  // 成功色扩展
  --td-success-color-hover: var(--td-success-color-4);
  --td-success-color-focus: var(--td-success-color-2);
  --td-success-color-active: var(--td-success-color-6);
  --td-success-color-disabled: var(--td-success-color-3);
  --td-success-color-light: var(--td-success-color-1);
  --td-success-color-light-hover: var(--td-success-color-2);

  // 遮罩
  --td-mask-active: rgba(0, 0, 0, 40%); // 遮罩-弹出
  --td-mask-disabled: rgba(0, 0, 0, 60%); // 遮罩-禁用
  --td-mask-background: rgba(36, 36, 36, 96%); // 二维码遮罩

  // 背景色
  --td-bg-color-page: var(--td-gray-color-14);                        // 色彩 - page
  --td-bg-color-container: var(--td-gray-color-13);                   // 色彩 - 容器
  --td-bg-color-container-hover: var(--td-gray-color-12);             // 色彩 - 容器 - hover
  --td-bg-color-container-active: var(--td-gray-color-10);            // 色彩 - 容器 - active
  --td-bg-color-container-select: var(--td-gray-color-9);             // 色彩 - 容器 - select
  --td-bg-color-secondarycontainer: var(--td-gray-color-12);          // 色彩 - 次级容器
  --td-bg-color-secondarycontainer-hover: var(--td-gray-color-11);    // 色彩 - 次级容器 - hover
  --td-bg-color-secondarycontainer-active: var(--td-gray-color-9);   // 色彩 - 次级容器 - active
  --td-bg-color-component: var(--td-gray-color-11);                   // 色彩  - 组件
  --td-bg-color-component-hover: var(--td-gray-color-10);             // 色彩 - 组件 - hover
  --td-bg-color-component-active: var(--td-gray-color-9);             // 色彩 - 组件 - active
  --td-bg-color-secondarycomponent: var(--td-gray-color-10);          // 色彩 - 次级组件
  --td-bg-color-secondarycomponent-hover: var(--td-gray-color-9);    // 色彩 - 次级组件 - hover
  --td-bg-color-secondarycomponent-active: var(--td-gray-color-8);   // 色彩 - 次级组件 - active
  --td-bg-color-component-disabled: var(--td-gray-color-12);          // 色彩 - 组件 - disabled

  // 特殊组件背景色，目前只用于 button、input 组件多主题场景，浅色主题下固定为白色，深色主题下为 transparent 适配背景颜色
  --td-bg-color-specialcomponent: transparent;

  // 文本颜色
  --td-text-color-primary: var(--td-font-white-1);      // 色彩-文字-主要
  --td-text-color-secondary: var(--td-font-white-2);    // 色彩-文字-次要
  --td-text-color-placeholder: var(--td-font-white-3);  // 色彩-文字-占位符/说明
  --td-text-color-disabled: var(--td-font-white-4);    // 色彩-文字-禁用
  --td-text-color-anti: #fff;                      // 色彩-文字-反色
  --td-text-color-brand: var(--td-brand-color-8);           // 色彩-文字-品牌
  --td-text-color-link: var(--td-brand-color-8);            // 色彩-文字-链接

  // 分割线
  --td-border-level-1-color: var(--td-gray-color-11);
  --td-component-stroke: var(--td-gray-color-11);
  // 边框
  --td-border-level-2-color: var(--td-gray-color-9);
  --td-component-border: var(--td-gray-color-9);

  // 基础/下层 投影 hover 使用的组件包括：表格 /
  --td-shadow-1: 0 4px 6px rgba(0, 0, 0, 6%), 0 1px 10px rgba(0, 0, 0, 8%), 0 2px 4px rgba(0, 0, 0, 12%);
  // 中层投影 下拉 使用的组件包括：下拉菜单 / 气泡确认框 / 选择器 /
  --td-shadow-2: 0 8px 10px rgba(0, 0, 0, 12%), 0 3px 14px rgba(0, 0, 0, 10%), 0 5px 5px rgba(0, 0, 0, 16%);
  // 上层投影（警示/弹窗）使用的组件包括：全局提示 / 消息通知
  --td-shadow-3: 0 16px 24px rgba(0, 0, 0, 14%), 0 6px 30px rgba(0, 0, 0, 12%), 0 8px 10px rgba(0, 0, 0, 20%);
  // 内投影 用于弹窗类组件（气泡确认框 / 全局提示 / 消息通知）的内描边

  --td-shadow-inset-top: inset 0 .5px 0 #5e5e5e;
  --td-shadow-inset-right: inset .5px 0 0 #5e5e5e;
  --td-shadow-inset-bottom: inset 0 -.5px 0 #5e5e5e;
  --td-shadow-inset-left: inset -.5px 0 0 #5e5e5e;

  // table 特定阴影
  --td-table-shadow-color: rgba(0, 0, 0, 55%);

  // 滚动条颜色
  --td-scrollbar-color: rgba(255, 255, 255, 10%);
  // 滚动条悬浮颜色（ hover ）
  --td-scrollbar-hover-color: rgba(255, 255, 255, 30%);
  // 滚动条轨道颜色，不能是带透明度，否则纵向滚动时，横向滚动条会穿透
  --td-scroll-track-color: #333;
}