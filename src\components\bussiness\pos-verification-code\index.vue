<template>
  <div class="verification-container">
    <template v-for="(code, index) in verificationCodes" :key="index">
      <!-- 输入框 -->
      <div class="verification-item">
        <input
          :ref="el => setInputRef(el as HTMLInputElement, index)"
          v-model="verificationCodes[index]"
          :disabled="disabled"
          :readonly="readonly"
          maxlength="1"
          class="verification-input t-input"
          type="text"
          autocomplete="off"
          @input="handleInput(index, $event as InputEvent)"
          @keydown="handleKeyDown(index, $event as KeyboardEvent)"
          @paste="handlePaste($event as ClipboardEvent)"
          @focus="handleFocus(index)"
        />
      </div>

      <!-- 分隔符 -->
      <div v-if="showSeparator && separatorPositions.includes(index)" class="verification-separator">
        <slot name="separator" :index="index">
          {{ separatorText }}
        </slot>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
/**
 * @file POS验证码输入组件
 * @description 支持自定义长度、分隔符位置的验证码输入组件
 * <AUTHOR>
 */

import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { verificationCodeProps, type VerificationCodeEmits } from './props';

// 组件选项
defineOptions({
  name: 'PosVerificationCode'
});

// Props 定义
const props = defineProps(verificationCodeProps);

// Emits 定义
const emit = defineEmits<VerificationCodeEmits>();

// 输入框引用数组
const inputRefs = ref<HTMLInputElement[]>([]);

// 验证码数组
const verificationCodes = ref<string[]>(Array(props.length).fill(''));

// 当前焦点输入框索引
const currentFocusIndex = ref<number>(-1);

/**
 * 设置输入框引用
 * @param el - 输入框元素
 * @param index - 索引
 */
const setInputRef = (el: HTMLInputElement | null, index: number): void => {
  if (el) {
    inputRefs.value[index] = el;
  }
};

/**
 * 计算完整的验证码值
 */
const codeValue = computed<string>(() => verificationCodes.value.join(''));

/**
 * 检查验证码是否完整
 */
const isComplete = computed<boolean>(() => codeValue.value.length === props.length);

/**
 * 处理输入事件
 * @param index - 输入框索引
 * @param event - 输入事件
 */
const handleInput = (index: number, event: InputEvent): void => {
  const target = event.target as HTMLInputElement;
  let value = target.value;

  // 只保留数字和字母
  value = value.replace(/[^a-zA-Z0-9]/g, '');

  // 确保只有一个字符
  if (value.length > 1) {
    value = value.slice(0, 1);
  }

  // 更新验证码
  verificationCodes.value[index] = value;
  target.value = value;

  // 触发更新事件
  emit('update:modelValue', codeValue.value);
  emit('change', codeValue.value);

  // 检查是否完成
  if (isComplete.value) {
    emit('complete', codeValue.value);
  }

  // 自动跳转到下一个输入框
  if (value && index < props.length - 1) {
    focusInput(index + 1);
  }
};

/**
 * 处理键盘事件
 * @param index - 输入框索引
 * @param event - 键盘事件
 */
const handleKeyDown = (index: number, event: KeyboardEvent): void => {
  const target = event.target as HTMLInputElement;

  // 处理退格键
  if (event.key === 'Backspace') {
    // 如果当前输入框为空且不是第一个，跳转到前一个
    if (!target.value && index > 0) {
      event.preventDefault();
      focusInput(index - 1);
      // 清空前一个输入框的值
      verificationCodes.value[index - 1] = '';
      emit('update:modelValue', codeValue.value);
      emit('change', codeValue.value);
    }
  }

  // 处理左右箭头键
  if (event.key === 'ArrowLeft' && index > 0) {
    event.preventDefault();
    focusInput(index - 1);
  }

  if (event.key === 'ArrowRight' && index < props.length - 1) {
    event.preventDefault();
    focusInput(index + 1);
  }

  // 阻止非数字字母字符输入
  if (
    !/[a-zA-Z0-9]/.test(event.key) &&
    !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight'].includes(event.key)
  ) {
    event.preventDefault();
  }
};

/**
 * 处理粘贴事件
 * @param event - 粘贴事件
 */
const handlePaste = (event: ClipboardEvent): void => {
  event.preventDefault();

  const pastedData = event.clipboardData?.getData('text') || '';
  const cleanData = pastedData.replace(/[^a-zA-Z0-9]/g, '').slice(0, props.length);

  // 分配粘贴的数据到各个输入框
  for (let i = 0; i < props.length; i++) {
    verificationCodes.value[i] = cleanData[i] || '';
  }

  // 更新所有输入框的值
  inputRefs.value.forEach((input, index) => {
    if (input) {
      input.value = verificationCodes.value[index];
    }
  });

  // 触发事件
  emit('update:modelValue', codeValue.value);
  emit('change', codeValue.value);

  // 如果完成则触发完成事件，否则聚焦到下一个空输入框
  if (isComplete.value) {
    emit('complete', codeValue.value);
  } else {
    const nextEmptyIndex = verificationCodes.value.findIndex(code => !code);
    if (nextEmptyIndex !== -1) {
      focusInput(nextEmptyIndex);
    }
  }
};

/**
 * 处理焦点事件
 * @param index - 输入框索引
 */
const handleFocus = (index: number): void => {
  currentFocusIndex.value = index;
  // 选中输入框内容
  nextTick(() => {
    inputRefs.value[index]?.select();
  });
};

/**
 * 聚焦指定输入框
 * @param index - 输入框索引
 */
const focusInput = (index: number): void => {
  if (index >= 0 && index < props.length) {
    nextTick(() => {
      inputRefs.value[index]?.focus();
    });
  }
};

/**
 * 清空所有输入框
 */
const clear = (): void => {
  verificationCodes.value = Array(props.length).fill('');
  inputRefs.value.forEach(input => {
    if (input) input.value = '';
  });
  emit('update:modelValue', '');
  emit('change', '');
  focusInput(0);
};

/**
 * 设置验证码值
 * @param value - 验证码值
 */
const setValue = (value: string): void => {
  const cleanValue = value.replace(/[^a-zA-Z0-9]/g, '').slice(0, props.length);

  for (let i = 0; i < props.length; i++) {
    verificationCodes.value[i] = cleanValue[i] || '';
  }

  inputRefs.value.forEach((input, index) => {
    if (input) {
      input.value = verificationCodes.value[index];
    }
  });
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newValue => {
    if (newValue !== codeValue.value) {
      setValue(newValue);
    }
  },
  { immediate: true }
);

// 监听长度变化
watch(
  () => props.length,
  newLength => {
    const currentLength = verificationCodes.value.length;

    if (newLength > currentLength) {
      // 增加长度
      verificationCodes.value.push(...Array(newLength - currentLength).fill(''));
    } else if (newLength < currentLength) {
      // 减少长度
      verificationCodes.value = verificationCodes.value.slice(0, newLength);
    }
  }
);

// 挂载后聚焦第一个输入框
onMounted(() => {
  if (!props.disabled && !props.readonly) {
    focusInput(0);
  }
});

// 暴露组件方法
defineExpose({
  clear,
  setValue,
  focus: () => focusInput(0),
  blur: () => inputRefs.value[currentFocusIndex.value]?.blur()
});
</script>

<style lang="less" scoped>
/**
 * POS验证码输入组件样式
 * 支持主题切换和自定义分隔符
 */

.verification-container {
  display: flex;
  align-items: center;
  gap: var(--td-comp-size-md, 8px);
  font-family: var(--td-font-family, 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif);

  .verification-item {
    flex: 1;
    position: relative;
  }
}

.verification-input {
  width: 100%;
  height: var(--td-comp-size-l, 40px);
  padding: 0;
  font-size: var(--td-font-size-base, 16px);
  font-weight: var(--td-font-weight-normal, 400);
  line-height: var(--td-line-height-base, 1.5);
  text-align: center;
  color: var(--td-text-color-primary, #000000d9);
  background-color: var(--td-bg-color-container, #ffffff);
  border: 1px solid var(--td-border-color, #dcdcdc);
  border-radius: var(--td-radius-default, 6px);
  outline: none;
  transition: all var(--td-transition, 0.2s) ease;

  /* 移除默认样式 */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: textfield;

  /* 禁用浏览器默认的自动填充样式 */
  &:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px var(--td-bg-color-container, #ffffff) inset;
    -webkit-text-fill-color: var(--td-text-color-primary, #000000d9);
  }

  /* 悬浮状态 */
  &:hover:not(:disabled):not(:readonly) {
    border-color: var(--td-brand-color-hover, #4582e6);
  }

  /* 聚焦状态 */
  &:focus {
    border-color: var(--td-brand-color, #0052d9);
    box-shadow: 0 0 0 2px var(--td-brand-color-focus, rgba(5, 82, 217, 0.1));
  }

  /* 禁用状态 */
  &:disabled {
    color: var(--td-text-color-disabled, #00000040);
    background-color: var(--td-bg-color-component-disabled, #f5f5f5);
    border-color: var(--td-border-color, #dcdcdc);
    cursor: not-allowed;
  }

  /* 只读状态 */
  &:readonly {
    background-color: var(--td-bg-color-component-disabled, #f5f5f5);
    cursor: default;
  }

  /* 错误状态 */
  &.error {
    border-color: var(--td-error-color, #d54941);

    &:focus {
      border-color: var(--td-error-color, #d54941);
      box-shadow: 0 0 0 2px var(--td-error-color-focus, rgba(213, 73, 65, 0.1));
    }
  }

  /* 成功状态 */
  &.success {
    border-color: var(--td-success-color, #00a870);

    &:focus {
      border-color: var(--td-success-color, #00a870);
      box-shadow: 0 0 0 2px var(--td-success-color-focus, rgba(0, 168, 112, 0.1));
    }
  }
}

.verification-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: var(--td-comp-size-xs, 16px);
  height: var(--td-comp-size-l, 40px);
  font-size: var(--td-font-size-base, 14px);
  font-weight: var(--td-font-weight-normal, 400);
  color: var(--td-text-color-secondary, #00000073);
  user-select: none;

  /* 分隔符动画效果 */
  transition: color var(--td-transition, 0.2s) ease;

  /* 主题适配 */
  [theme-mode='dark'] & {
    color: var(--td-text-color-secondary, #ffffff73);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verification-container {
    gap: var(--td-comp-size-xxs, 4px);
  }

  .verification-input {
    height: var(--td-comp-size-m, 32px);
    font-size: var(--td-font-size-s, 12px);
  }

  .verification-separator {
    min-width: var(--td-comp-size-xxs, 12px);
    height: var(--td-comp-size-m, 32px);
    font-size: var(--td-font-size-s, 12px);
  }
}

/* 动画效果 */
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-2px);
  }

  75% {
    transform: translateX(2px);
  }
}

.verification-container.shake {
  animation: shake 0.3s ease-in-out;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .verification-input {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .verification-input,
  .verification-separator {
    transition: none;
  }

  .verification-container.shake {
    animation: none;
  }
}
</style>
