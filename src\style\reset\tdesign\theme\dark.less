:root[theme-mode="dark"] {
  // 基于 Figma 设计的品牌色系 - 使用现有12级映射到10级 (暗色主题)
  --td-brand-color-1: var(--brand-25);    // 最浅 - 在暗色主题中用于文本
  --td-brand-color-2: var(--brand-50);    // 极浅
  --td-brand-color-3: var(--brand-100);   // 很浅
  --td-brand-color-4: var(--brand-200);   // 浅
  --td-brand-color-5: var(--brand-300);   // 中浅
  --td-brand-color-6: var(--brand-400);   // 中等
  --td-brand-color-7: var(--brand-500);   // 主要品牌色 - 在暗色主题中更亮
  --td-brand-color-8: var(--brand-600);   // 深
  --td-brand-color-9: var(--brand-700);   // 很深
  --td-brand-color-10: var(--brand-950);  // 最深 - 背景用

  // 基于 Figma 设计的警告色系 - 使用现有12级映射到10级 (暗色主题)
  --td-warning-color-1: var(--warning-25);   // 最浅 - 暗色主题中用于文本
  --td-warning-color-2: var(--warning-50);   // 极浅
  --td-warning-color-3: var(--warning-100);  // 很浅
  --td-warning-color-4: var(--warning-200);  // 浅
  --td-warning-color-5: var(--warning-400);  // 中等
  --td-warning-color-6: var(--warning-500);  // 主要警告色
  --td-warning-color-7: var(--warning-600);  // 深
  --td-warning-color-8: var(--warning-700);  // 很深
  --td-warning-color-9: var(--warning-800);  // 极深
  --td-warning-color-10: var(--warning-950); // 最深 - 背景用
  
  // 基于 Figma 设计的错误色系 - 使用现有12级映射到10级 (暗色主题)
  --td-error-color-1: var(--error-25);    // 最浅 - 暗色主题中用于文本
  --td-error-color-2: var(--error-50);    // 极浅
  --td-error-color-3: var(--error-100);   // 很浅
  --td-error-color-4: var(--error-200);   // 浅
  --td-error-color-5: var(--error-400);   // 中等
  --td-error-color-6: var(--error-500);   // 主要错误色
  --td-error-color-7: var(--error-600);   // 深
  --td-error-color-8: var(--error-700);   // 很深
  --td-error-color-9: var(--error-800);   // 极深
  --td-error-color-10: var(--error-950);  // 最深 - 背景用

  // 基于 Figma 设计的成功色系 - 使用现有12级映射到10级 (暗色主题)
  --td-success-color-1: var(--success-25);   // 最浅 - 暗色主题中用于文本
  --td-success-color-2: var(--success-50);   // 极浅
  --td-success-color-3: var(--success-100);  // 很浅
  --td-success-color-4: var(--success-200);  // 浅
  --td-success-color-5: var(--success-400);  // 中等
  --td-success-color-6: var(--success-500);  // 主要成功色
  --td-success-color-7: var(--success-600);  // 深
  --td-success-color-8: var(--success-700);  // 很深
  --td-success-color-9: var(--success-800);  // 极深
  --td-success-color-10: var(--success-950); // 最深 - 背景用
  
  // 基于 Figma 设计的灰色系 - 使用现有12级映射到14级 (暗色主题)
  // 在暗色主题中，背景使用深色，文本使用浅色
  --td-gray-color-1: var(--gray-dark-25);      // 文本用 - 最浅
  --td-gray-color-2: var(--gray-dark-50);      // 文本用 - 极浅  
  --td-gray-color-3: var(--gray-dark-100);     // 文本用 - 很浅
  --td-gray-color-4: var(--gray-dark-200);     // 文本用 - 浅
  --td-gray-color-5: var(--gray-dark-300);     // 文本用 - 浅 (复用200)
  --td-gray-color-6: var(--gray-dark-400);     // 界面元素 - 中浅
  --td-gray-color-7: var(--gray-dark-500);     // 界面元素 - 中等
  --td-gray-color-8: var(--gray-dark-550);     // 界面元素 - 中深
  --td-gray-color-9: var(--gray-dark-600);     // 背景元素 - 深
  --td-gray-color-10: var(--gray-dark-700);    // 背景元素 - 很深
  --td-gray-color-11: var(--gray-dark-800);    // 背景用 - 极深
  --td-gray-color-12: var(--gray-dark-850);    // 背景用 - 最深
  --td-gray-color-13: var(--gray-dark-900);    // 背景用 - 接近黑色
  --td-gray-color-14: var(--gray-dark-950);    // 背景用 - 最深 (复用950)

  // 文字 & 图标 颜色
  --td-font-white-1: rgba(255, 255, 255, 90%);
  --td-font-white-2: rgba(255, 255, 255, 55%);
  --td-font-white-3: rgba(255, 255, 255, 35%);
  --td-font-white-4: rgba(255, 255, 255, 22%);
  --td-font-gray-1: rgba(0, 0, 0, 90%);
  --td-font-gray-2: rgba(0, 0, 0, 60%);
  --td-font-gray-3: rgba(0, 0, 0, 40%);
  --td-font-gray-4: rgba(0, 0, 0, 26%);

  // 基础颜色
  --td-brand-color: var(--td-brand-color-8);     // 色彩-品牌-可操作
  --td-warning-color: var(--td-warning-color-5); // 色彩-功能-警告
  --td-error-color: var(--td-error-color-6);     // 色彩-功能-失败
  --td-success-color: var(--td-success-color-5); // 色彩-功能-成功

  // 基础颜色的扩展 用于 hover / 聚焦 / 禁用 / 点击 等状态
  --td-brand-color-hover: var(--td-brand-color-7);     // hover态
  --td-brand-color-focus: var(--td-brand-color-2);     // focus态，包括鼠标和键盘
  --td-brand-color-active: var(--td-brand-color-9);    // 点击态
  --td-brand-color-disabled: var(--td-brand-color-3);  // 禁用态
  --td-brand-color-light: var(--td-brand-color-1);     // 浅色的选中态
  --td-brand-color-light-hover: var(--td-brand-color-2);

  // 警告色扩展
  --td-warning-color-hover: var(--td-warning-color-4);
  --td-warning-color-focus: var(--td-warning-color-2);
  --td-warning-color-active: var(--td-warning-color-6);
  --td-warning-color-disabled: var(--td-warning-color-3);
  --td-warning-color-light: var(--td-warning-color-1);
  --td-warning-color-light-hover: var(--td-warning-color-2);

  // 失败/错误色扩展
  --td-error-color-hover: var(--td-error-color-5);
  --td-error-color-focus: var(--td-error-color-2);
  --td-error-color-active: var(--td-error-color-7);
  --td-error-color-disabled: var(--td-error-color-3);
  --td-error-color-light: var(--td-error-color-1);
  --td-error-color-light-hover: var(--td-error-color-2);

  // 成功色扩展
  --td-success-color-hover: var(--td-success-color-4);
  --td-success-color-focus: var(--td-success-color-2);
  --td-success-color-active: var(--td-success-color-6);
  --td-success-color-disabled: var(--td-success-color-3);
  --td-success-color-light: var(--td-success-color-1);
  --td-success-color-light-hover: var(--td-success-color-2);

  // 遮罩
  --td-mask-active: rgba(0, 0, 0, 40%); // 遮罩-弹出
  --td-mask-disabled: rgba(0, 0, 0, 60%); // 遮罩-禁用
  --td-mask-background: rgba(36, 36, 36, 96%); // 二维码遮罩

  // 背景色
  --td-bg-color-page: var(--td-gray-color-14);                        // 色彩 - page
  --td-bg-color-container: var(--td-gray-color-13);                   // 色彩 - 容器
  --td-bg-color-container-hover: var(--td-gray-color-12);             // 色彩 - 容器 - hover
  --td-bg-color-container-active: var(--td-gray-color-10);            // 色彩 - 容器 - active
  --td-bg-color-container-select: var(--td-gray-color-9);             // 色彩 - 容器 - select
  --td-bg-color-secondarycontainer: var(--td-gray-color-12);          // 色彩 - 次级容器
  --td-bg-color-secondarycontainer-hover: var(--td-gray-color-11);    // 色彩 - 次级容器 - hover
  --td-bg-color-secondarycontainer-active: var(--td-gray-color-9);   // 色彩 - 次级容器 - active
  --td-bg-color-component: var(--td-gray-color-11);                   // 色彩  - 组件
  --td-bg-color-component-hover: var(--td-gray-color-10);             // 色彩 - 组件 - hover
  --td-bg-color-component-active: var(--td-gray-color-9);             // 色彩 - 组件 - active
  --td-bg-color-secondarycomponent: var(--td-gray-color-10);          // 色彩 - 次级组件
  --td-bg-color-secondarycomponent-hover: var(--td-gray-color-9);    // 色彩 - 次级组件 - hover
  --td-bg-color-secondarycomponent-active: var(--td-gray-color-8);   // 色彩 - 次级组件 - active
  --td-bg-color-component-disabled: var(--td-gray-color-12);          // 色彩 - 组件 - disabled

  // 特殊组件背景色，目前只用于 button、input 组件多主题场景，浅色主题下固定为白色，深色主题下为 transparent 适配背景颜色
  --td-bg-color-specialcomponent: transparent;

  // 文本颜色
  --td-text-color-primary: var(--td-font-white-1);      // 色彩-文字-主要
  --td-text-color-secondary: var(--td-font-white-2);    // 色彩-文字-次要
  --td-text-color-placeholder: var(--td-font-white-3);  // 色彩-文字-占位符/说明
  --td-text-color-disabled: var(--td-font-white-4);    // 色彩-文字-禁用
  --td-text-color-anti: #fff;                      // 色彩-文字-反色
  --td-text-color-brand: var(--td-brand-color-8);           // 色彩-文字-品牌
  --td-text-color-link: var(--td-brand-color-8);            // 色彩-文字-链接

  // 分割线
  --td-border-level-1-color: var(--td-gray-color-11);
  --td-component-stroke: var(--td-gray-color-11);
  // 边框
  --td-border-level-2-color: var(--td-gray-color-9);
  --td-component-border: var(--td-gray-color-9);

  // 基础/下层 投影 hover 使用的组件包括：表格 /
  --td-shadow-1: 0 4px 6px rgba(0, 0, 0, 6%), 0 1px 10px rgba(0, 0, 0, 8%), 0 2px 4px rgba(0, 0, 0, 12%);
  // 中层投影 下拉 使用的组件包括：下拉菜单 / 气泡确认框 / 选择器 /
  --td-shadow-2: 0 8px 10px rgba(0, 0, 0, 12%), 0 3px 14px rgba(0, 0, 0, 10%), 0 5px 5px rgba(0, 0, 0, 16%);
  // 上层投影（警示/弹窗）使用的组件包括：全局提示 / 消息通知
  --td-shadow-3: 0 16px 24px rgba(0, 0, 0, 14%), 0 6px 30px rgba(0, 0, 0, 12%), 0 8px 10px rgba(0, 0, 0, 20%);
  // 内投影 用于弹窗类组件（气泡确认框 / 全局提示 / 消息通知）的内描边

  --td-shadow-inset-top: inset 0 .5px 0 #5e5e5e;
  --td-shadow-inset-right: inset .5px 0 0 #5e5e5e;
  --td-shadow-inset-bottom: inset 0 -.5px 0 #5e5e5e;
  --td-shadow-inset-left: inset -.5px 0 0 #5e5e5e;

  // table 特定阴影
  --td-table-shadow-color: rgba(0, 0, 0, 55%);

  // 滚动条颜色
  --td-scrollbar-color: rgba(255, 255, 255, 10%);
  // 滚动条悬浮颜色（ hover ）
  --td-scrollbar-hover-color: rgba(255, 255, 255, 30%);
  // 滚动条轨道颜色，不能是带透明度，否则纵向滚动时，横向滚动条会穿透
  --td-scroll-track-color: #333;
}