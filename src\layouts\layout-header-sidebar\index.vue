<template>
  <div class="header-sidebar-layout">
    <div class="header">
      <slot name="header">
        <!-- 顶栏内容 -->
      </slot>
    </div>
    <div class="body">
      <div class="sidebar-left">
        <slot name="sidebar">
          <!-- 左侧边栏内容 -->
        </slot>
      </div>
      <div class="main-content">
        <alive-router-view>
          <slot name="main"></slot>
        </alive-router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { AliveRouterView } from '@/components';

// 顶栏 header，下部是左右布局（左400px，右侧自适应）
</script>

<style lang="less" scoped>
@import './index.less';
</style>
