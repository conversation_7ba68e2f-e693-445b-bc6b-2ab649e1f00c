// --- 核心依赖 ---
// Vue.js 框架的核心部分，用于创建 Vue 应用实例。
import { createApp } from 'vue';
// TDesign 组件库，用于构建用户界面。
import TDesign from 'tdesign-vue-next';

// --- 应用程序模块 ---
// 根组件，是所有其他组件的父组件。
import App from './App.vue';
// Vuex 状态管理，用于管理应用级别的状态。
import { store } from './store';
// Vue Router 路由管理，用于处理应用的页面导航。
import router from './router';
// i18n 国际化支持，用于多语言切换。
import { createI18nInstance } from './locale';
// 前端追踪日志
import { createTracking } from '@/utils/tracking';

// --- 样式表 ---
// TDesign 组件库的基础样式。
import 'tdesign-vue-next/es/style/index.css';
// 项目的全局自定义样式。
import '@/style/index.less';

// 创建 Vue 应用实例
const app = createApp(App);

// 注册插件
app.use(TDesign);
app.use(store); // 先注册 Pinia store

// 在 Pinia 初始化后创建 i18n 实例
const i18n = createI18nInstance();

app.use(router);
app.use(i18n);

// 挂载应用
// 将 Vue 实例挂载到 id 为 'app' 的 DOM 元素上。
app.mount('#app');

createTracking(app);
