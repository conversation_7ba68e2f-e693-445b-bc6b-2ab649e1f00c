export default {
  component: {
    cancel: '取消',
    action: '确定',
    save: '保存',
    edit: '编辑',
    search: '搜索',
    posAreaCascader: {
      placeholder: '请选择省/市/区',
      selectArea: '请选择您的地区',
      defaultDemo: '默认配置',
      multipleDemo: '多选模式',
      sizeDemo: '不同尺寸',
      smallSize: '小尺寸',
      mediumSize: '中尺寸',
      largeSize: '大尺寸',
      selectedValue: '选中值'
    }
  },
  message: {
    error: '服务器内部错误',
    error0: '网络错误',
    error400: '拒绝访问',
    error401: '未授权访问',
    error403: '请求错误',
    error404: '请求地址出错',
    error408: '请求超时',
    error500: '服务器内部错误',
    error501: '服务未实现',
    error502: '网关错误',
    error503: '服务不可用',
    error504: '网关超时',
    error505: 'HTTP版本不受支持'
  },
  login: {
    title: '登录到',
    haveAccount: '已有账号?',
    noAccount: '没有账号吗?',
    signIn: '登录',
    registerNewAccount: '注册新账号',
    copyright: 'Copyright © 2025 创迹软件有限公司',

    // 欢迎信息
    welcome: {
      title: '欢迎使用 掌柜智囊',
      subtitle: '请选择您的登录方式'
    },

    // 标签页
    tabs: {
      qrLogin: '扫码登录',
      accountLogin: '账号登录'
    },

    // 扫码登录
    qr: {
      scanTip: '请使用"微信"扫一扫',
      refresh: '刷新'
    },

    // 账号登录
    account: {
      phoneLabel: '手机号',
      phonePlaceholder: '请填写管理员手机号',
      passwordLabel: '密码',
      passwordPlaceholder: '请输入密码',
      rememberPassword: '记住密码',
      forgotPassword: '忘记密码',
      loginButton: '登录',
      noAccount: '还没有账号？',
      registerNow: '立即注册'
    },

    // 忘记密码
    forgot: {
      title: '忘记密码',
      subtitle: '别担心，我们将协助您重置密码',
      phoneLabel: '手机号',
      phonePlaceholder: '请填写管理员手机号',
      codeLabel: '验证码',
      newPasswordLabel: '新密码',
      newPasswordPlaceholder: '请输入新密码',
      sendCode: '获取验证码',
      sendingCode: '发送中...',
      resendAfter: 's后重发',
      resetButton: '重置密码',
      backToLogin: '返回登录',
      requirements: {
        length: '必须包含 8 个字符',
        complexity: '必须至少包含 1个数字、字母'
      }
    },

    // 重置成功
    resetSuccess: {
      title: '密码重置成功',
      subtitle: '您的密码重置成功，点击下方即可快速登录',
      continueButton: '继续',
      backToLogin: '返回登录'
    },

    form: {
      accountPlaceholder: '请输入账号：admin',
      passwordPlaceholder: '请输入登录密码：admin',
      rememberAccount: '记住账号',
      forgotAccount: '忘记账号？',
      wechatScan: '请使用微信扫一扫登录',
      refresh: '刷新',
      phonePlaceholder: '请输入手机号码',
      verifyCodePlaceholder: '请输入验证码',
      sendVerifyCode: '发送验证码',
      resendAfter: '秒后可重发',
      loginButton: '登录',
      useAccountPassword: '使用账号密码登录',
      useWechatScan: '使用微信扫码登录',
      usePhone: '使用手机号登录'
    },

    register: {
      phonePlaceholder: '请输入您的手机号',
      emailPlaceholder: '请输入您的邮箱',
      passwordPlaceholder: '请输入登录密码',
      haveReadAndAgree: '我已阅读并同意',
      serviceAgreement: 'TDesign服务协议',
      privacyStatement: 'TDesign 隐私声明',
      registerButton: '注册',
      useEmail: '使用邮箱注册',
      usePhone: '使用手机号注册'
    },

    validation: {
      phoneRequired: '手机号必填',
      accountRequired: '账号必填',
      passwordRequired: '密码必填',
      verifyCodeRequired: '验证码必填',
      emailRequired: '邮箱必填',
      emailInvalid: '请输入正确的邮箱'
    },

    message: {
      loginSuccess: '登陆成功',
      registerSuccess: '注册成功',
      mustAgree: '请同意TDesign服务协议和TDesign 隐私声明'
    }
  },
  develop: {
    develop: '开发'
  },
  app: {
    theme: '主题',
    component: '组件',
    language: '语言'
  },
  customerService: {
    contact: '联系客服',
    title: '专属售后客服',
    description: '"产品使用疑问、故障排查，我都会为您全力解决。"',
    wechatTip: '打开微信"扫一扫"，添加专属客服',
    phoneNumber: '400-0330-2520 转 2',
    workTime: '客服工作时间：8:00-17:00',
    confirm: '知道了'
  },
  result: {
    notFound: {
      title: '404 Not Found',
      tip: '抱歉，您访问的页面不存在',
      backHome: '返回首页'
    }
  }
};
