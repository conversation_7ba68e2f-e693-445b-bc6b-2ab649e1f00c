export default {
  component: {
    cancel: '取消',
    action: '确定',
    save: '保存',
    edit: '编辑',
    search: '搜索'
  },
  message: {
    error: '服务器内部错误',
    error0: '网络错误',
    error400: '拒绝访问',
    error401: '未授权访问',
    error403: '请求错误',
    error404: '请求地址出错',
    error408: '请求超时',
    error500: '服务器内部错误',
    error501: '服务未实现',
    error502: '网关错误',
    error503: '服务不可用',
    error504: '网关超时',
    error505: 'HTTP版本不受支持'
  },
  login: {
    title: '登录到',
    haveAccount: '已有账号?',
    noAccount: '没有账号吗?',
    signIn: '登录',
    registerNewAccount: '注册新账号',
    copyright: 'Copyright © 2025 创迹软件有限公司',

    form: {
      accountPlaceholder: '请输入账号：admin',
      passwordPlaceholder: '请输入登录密码：admin',
      rememberAccount: '记住账号',
      forgotAccount: '忘记账号？',
      wechatScan: '请使用微信扫一扫登录',
      refresh: '刷新',
      phonePlaceholder: '请输入手机号码',
      verifyCodePlaceholder: '请输入验证码',
      sendVerifyCode: '发送验证码',
      resendAfter: '秒后可重发',
      loginButton: '登录',
      useAccountPassword: '使用账号密码登录',
      useWechatScan: '使用微信扫码登录',
      usePhone: '使用手机号登录'
    },

    register: {
      phonePlaceholder: '请输入您的手机号',
      emailPlaceholder: '请输入您的邮箱',
      passwordPlaceholder: '请输入登录密码',
      haveReadAndAgree: '我已阅读并同意',
      serviceAgreement: 'TDesign服务协议',
      privacyStatement: 'TDesign 隐私声明',
      registerButton: '注册',
      useEmail: '使用邮箱注册',
      usePhone: '使用手机号注册'
    },

    validation: {
      phoneRequired: '手机号必填',
      accountRequired: '账号必填',
      passwordRequired: '密码必填',
      verifyCodeRequired: '验证码必填',
      emailRequired: '邮箱必填',
      emailInvalid: '请输入正确的邮箱'
    },

    message: {
      loginSuccess: '登陆成功',
      registerSuccess: '注册成功',
      mustAgree: '请同意TDesign服务协议和TDesign 隐私声明'
    }
  },
  develop: {
    develop: '开发'
  },
  customerService: {
    contact: '联系客服',
    title: '专属售后客服',
    description: '"产品使用疑问、故障排查，我都会为您全力解决。"',
    wechatTip: '打开微信"扫一扫"，添加专属客服',
    phoneNumber: '400-0330-2520 转 2',
    workTime: '客服工作时间：8:00-17:00',
    confirm: '知道了'
  }
};
