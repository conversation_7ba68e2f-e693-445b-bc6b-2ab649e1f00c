<template>
  <div class="left-sidebar-layout">
    <div class="sidebar-left">
      <slot name="sidebar">
        <!-- 左侧边栏内容 -->
      </slot>
    </div>
    <div class="main-content">
      <alive-router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { AliveRouterView } from '@/components';

// 左右布局，左侧固定400px
</script>

<style lang="less" scoped>
@import './index.less';
</style>
