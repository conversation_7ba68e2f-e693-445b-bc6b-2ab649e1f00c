// 公共组件导入和导出
// 这里统一管理所有可复用的公共组件，方便其他模块引用

// 缓存路由视图组件 - 用于保持路由状态
import AliveRouterView from './base/alive-router-view/index.vue';

// 页面结果组件 - 用于显示页面状态（404、500等）
import PageResult from './base/page-result/index.vue';

// Markdown 查看器组件 - 用于渲染 Markdown 内容
import MarkdownViewer from './base/markdown-viewer/index.vue';

// 客服组件 - 提供客户服务功能
import PosCustomerService from './bussiness/pos-customer-service/index.vue';

// 头像组件 - 用户头像显示
import PosAvatar from './bussiness/pos-avatar/index.vue';

// 省区市选择组件 - 基于 t-cascader 的地区选择
import PosAreaCascader from './bussiness/pos-area-cascader/index.vue';

// 验证码输入组件 - 支持多位数字验证码输入
import PosVerificationCode from './bussiness/pos-verification-code/index.vue';

// 统一导出所有组件
export {
  AliveRouterView,
  PosCustomerService,
  PosAvatar,
  PosAreaCascader,
  PosVerificationCode,
  MarkdownViewer,
  PageResult
};
