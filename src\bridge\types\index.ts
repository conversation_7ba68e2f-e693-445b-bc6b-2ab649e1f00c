// --- 1. 底层 JSON-RPC 结构 ---
export interface JsonRpcRequest<T = any> {
  jsonrpc: '2.0';
  method: string;
  params?: T;
  id: string | number;
}
export interface JsonRpcNotification<T = any> {
  jsonrpc: '2.0';
  method: string;
  params?: T;
}
export interface JsonRpcSuccessResponse<T = any> {
  jsonrpc: '2.0';
  result: T;
  id: string | number;
}
export interface JsonRpcErrorResponse {
  jsonrpc: '2.0';
  error: { code: number; message: string; data?: any };
  id: string | number | null;
}
export type JsonRpcResponse<T = any> = JsonRpcSuccessResponse<T> | JsonRpcErrorResponse;

// --- 2. 标准化业务响应 (已在上方定义) ---
/**
 * 通用的成功响应结构。
 * @template T - 成功时返回的业务数据类型。
 */
export interface SuccessResponse<T> {
  /** 操作是否成功，固定为 true */
  success: true;
  /** 业务数据负载 */
  data: T;
}

/**
 * 通用的失败响应结构。
 */
export interface ErrorResponse {
  /** 操作是否成功，固定为 false */
  success: false;
  /**
   * 业务错误码，用于程序化识别错误类型。
   * 规范建议:
   * - 1000-1999: 通用错误 (如参数无效)
   * - 2000-2999: 打印机相关错误
   * - 3000-3999: 数据库相关错误
   */
  code: number;
  /** 人类可读的错误信息，用于日志记录或向用户展示。 */
  message: string;
}

// --- 3. 日志类型定义 ---
/**
 * 日志条目接口
 */
export interface LogEntry {
  /** 应用标识符 */
  appId: string;
  /** 设备唯一标识 */
  deviceKey: string;
  /** 日志条目唯一ID */
  id: string;
  /** 页面URL */
  pageUrl: string;
  /** 时间戳 */
  time: string;
  /** 用户ID */
  userId?: number;
  /** 用户名 */
  userName?: string;
  /** 日志类型 */
  type: 'vue-error' | 'api-error' | 'business-log' | 'performance' | string;
  /** 日志数据 */
  data: object;
}

// --- 4. 三种通信模式的类型定义 ---

/**
 * 模式1: Web 请求原生，需要有返回值
 * 使用 invoke 方法，返回 Promise
 */
export interface WebRequestNative {
  // 打印相关
  'printer.print': {
    params: { content: object; copies?: number };
    result: { jobId: string };
  };
  // 日志相关
  'logger.log': {
    params: { logs: LogEntry[] };
    result: { success: boolean };
  };
}

/**
 * 模式2: Web 通知原生，不需要有返回值
 * 使用 notify 方法，无返回值
 */
export interface WebNotifyNative {
  // 通知更新副屏内容
  'screen.update': {
    event: 'shopping' | 'other';
  };
}

/**
 * 模式3: 原生通知 Web，不需要有返回值
 * 使用 subscribe 方法，监听事件
 */
export interface NativeNotifyWeb {
  'screen.dataReceived': { event: 'shopping' | 'other'; data?: object };
}

// --- 5. 便于使用的类型别名 ---
export type WebRequestMethod = keyof WebRequestNative;
export type WebNotifyMethod = keyof WebNotifyNative;
export type NativeEventName = keyof NativeNotifyWeb;

// --- 6. Native API 接口定义 ---
/**
 * 完整的 Native API 接口
 * 按照三种通信模式组织
 */
export interface NativeApi {
  /**
   * 模式1: 请求原生并等待返回值
   */
  request: {
    [K in WebRequestMethod]: (
      _params: WebRequestNative[K]['params']
    ) => Promise<BridgeResponse<WebRequestNative[K]['result']>>;
  };

  /**
   * 模式2: 通知原生，内置错误处理
   */
  notify: {
    [K in WebNotifyMethod]: (_params: WebNotifyNative[K]) => BridgeResponse<null>;
  };

  /**
   * 模式3: 监听原生事件
   */
  on: {
    [K in NativeEventName]: (_listener: (_data: NativeNotifyWeb[K]) => void) => () => void;
  };

  /**
   * : 移除事件监听器
   */
  off: {
    [K in NativeEventName]: (_listener?: (_data: NativeNotifyWeb[K]) => void) => void;
  };
}

export type NativeApiResponse<T> = SuccessResponse<T> | ErrorResponse;

/**
 * 桥接层统一响应结构
 * 成功和失败都通过 resolve 返回，通过 success 字段区分
 */
export type BridgeResponse<T> =
  | { success: true; data: T }
  | { success: false; code?: number; message: string; data?: any };
