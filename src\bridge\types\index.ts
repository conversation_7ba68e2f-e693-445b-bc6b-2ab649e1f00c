// --- 1. 底层 JSON-RPC 结构 ---
export interface JsonRpcRequest<T = any> {
  jsonrpc: '2.0';
  method: string;
  params?: T;
  id: string | number;
}
export interface JsonRpcNotification<T = any> {
  jsonrpc: '2.0';
  method: string;
  params?: T;
}
export interface JsonRpcSuccessResponse<T = any> {
  jsonrpc: '2.0';
  result: T;
  id: string | number;
}
export interface JsonRpcErrorResponse {
  jsonrpc: '2.0';
  error: { code: number; message: string; data?: any };
  id: string | number | null;
}
export type JsonRpcResponse<T = any> = JsonRpcSuccessResponse<T> | JsonRpcErrorResponse;

// --- 2. 标准化业务响应 (已在上方定义) ---
/**
 * 通用的成功响应结构。
 * @template T - 成功时返回的业务数据类型。
 */
export interface SuccessResponse<T> {
  /** 操作是否成功，固定为 true */
  success: true;
  /** 业务数据负载 */
  data: T;
}

/**
 * 通用的失败响应结构。
 */
export interface ErrorResponse {
  /** 操作是否成功，固定为 false */
  success: false;
  /**
   * 业务错误码，用于程序化识别错误类型。
   * 规范建议:
   * - 1000-1999: 通用错误 (如参数无效)
   * - 2000-2999: 打印机相关错误
   * - 3000-3999: 数据库相关错误
   */
  code: number;
  /** 人类可读的错误信息，用于日志记录或向用户展示。 */
  message: string;
}

// --- 3. 三种通信模式的类型定义 ---

/**
 * 模式1: Web 请求原生，需要有返回值
 * 使用 invoke 方法，返回 Promise
 */
export interface WebRequestNative {
  // 打印相关
  'printer.print': {
    params: { content: object; copies?: number };
    result: { jobId: string };
  };
  // 日志相关
  'logger.log': {
    params: { context?: object };
    result: { success: boolean };
  };
  // 文件操作
  'file.save': {
    params: { path: string; content: string };
    result: { success: boolean; filePath: string };
  };
}

/**
 * 模式2: Web 通知原生，不需要有返回值
 * 使用 notify 方法，无返回值
 */
export interface WebNotifyNative {
  // UI 相关通知
  'ui.showToast': { message: string; duration: 'short' | 'long' };
  'ui.hideLoading': Record<string, never>;
  'ui.setTitle': { title: string };
  // 行为追踪
  'tracking.event': { event: string; properties?: object };
}

/**
 * 模式3: 原生通知 Web，不需要有返回值
 * 使用 subscribe 方法，监听事件
 */
export interface NativeNotifyWeb {
  // 扫码枪数据
  'scanner.dataReceived': { code: string; type: 'qr' | 'barcode' };
  // 网络状态变化
  'network.statusChanged': { online: boolean; type: 'wifi' | 'cellular' | 'ethernet' };
  // 设备状态
  'device.batteryChanged': { level: number; charging: boolean };
  // 应用生命周期
  'app.visibilityChanged': { visible: boolean };
}

// --- 4. 便于使用的类型别名 ---
export type WebRequestMethod = keyof WebRequestNative;
export type WebNotifyMethod = keyof WebNotifyNative;
export type NativeEventName = keyof NativeNotifyWeb;

// --- 5. Native API 接口定义 ---
/**
 * 完整的 Native API 接口
 * 按照三种通信模式组织
 */
export interface NativeApi {
  /**
   * 模式1: 请求原生并等待返回值
   */
  request: {
    [K in WebRequestMethod]: (
      _params: WebRequestNative[K]['params']
    ) => Promise<NativeApiResponse<WebRequestNative[K]['result']>>;
  };

  /**
   * 模式2: 通知原生，不等待返回值
   */
  notify: {
    [K in WebNotifyMethod]: (_params: WebNotifyNative[K]) => void;
  };

  /**
   * 模式3: 监听原生事件
   */
  on: {
    [K in NativeEventName]: (_listener: (_data: NativeNotifyWeb[K]) => void) => () => void;
  };
}

export type NativeApiResponse<T> = SuccessResponse<T> | ErrorResponse;
