<template>
  <router-view v-slot="{ Component }">
    <keep-alive :include="keepAliveList">
      <component :is="Component" v-if="Component" />
      <div v-else>
        <slot> </slot>
      </div>
    </keep-alive>
  </router-view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useConfigStore } from '@/store';

// 使用 Pinia 的 useConfigStore hook 获取全局配置状态
const configStore = useConfigStore();

// 计算属性，从 store 中获取需要被 <keep-alive> 缓存的组件列表
const keepAliveList = computed(() => configStore.keepAliveList);
</script>
