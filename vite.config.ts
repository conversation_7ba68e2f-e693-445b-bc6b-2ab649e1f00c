import { fileURLToPath, URL } from 'node:url';
import { ConfigEnv, defineConfig, UserConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import svgLoader from 'vite-svg-loader';
import markdown from 'unplugin-vue-markdown/vite'; // 导入插件

// https://vitejs.dev/config/
export default ({ mode }: ConfigEnv): UserConfig => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  const env = loadEnv(mode, process.cwd());

  return defineConfig({
    // publicPath, 部署生产环境和开发环境下的URL, VITE_BASE_URL可以在.env文件中配置
    base: env.VITE_BASE_URL || './',
    plugins: [
      vue({
        include: [/\.vue$/, /\.md$/] // 同样需要包含 .md 文件
      }),
      markdown({
        // 配置 markdown-it
        markdownItOptions: {
          html: true, // 允许在 markdown 中使用 HTML 标签
          linkify: true, // 自动将链接文字转换为 <a> 标签
          typographer: true // 启用智能标点符号替换
        }
      }),
      svgLoader() // https://github.com/jpkleemans/vite-svg-loader
    ],
    define: {
      'process.env': {}
    },
    // 路径别名
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    //配置代理,解决跨域问题
    // 开发服务器选项
    server: {
      host: '0.0.0.0',
      port: 8000, // 自定义端口
      open: true, // 自动打开浏览器
      // 配置代理，解决跨域问题
      proxy: {
        '/api': {
          // 代理目标地址
          target: 'http://yapi.trechina.cn/mock/1535',
          // 支持跨域
          changeOrigin: true
          // 重写路径，去掉 /api
          // rewrite: path => path.replace(/^\/api/, '')
        }
      }
    }
  });
};
