import globals from 'globals';
import tseslint from 'typescript-eslint';
import pluginVue from 'eslint-plugin-vue';

import vueScopedCss from 'eslint-plugin-vue-scoped-css';
import parserVue from 'vue-eslint-parser';
import pluginPrettier from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier';

export default tseslint.config(
  {
    // 全局忽略文件配置
    // 'ignores' 用于指定 ESLint 在 linting 过程中应该忽略的文件和目录。
    // 这对于排除不需要检查的文件（如 node_modules、构建输出、声明文件等）非常有用。
    ignores: ['node_modules', 'dist', 'build', 'public', '**/*.d.ts', '.specstory', '.cursor', '.husky']
  },
  // 基础配置
  // typescript-eslint 插件的推荐规则集，为 TypeScript 代码提供了基础的 linting 规则。
  ...tseslint.configs.recommended,
  // eslint-plugin-vue 插件的推荐规则集，专门用于检查 Vue.js 项目。
  ...pluginVue.configs['flat/recommended'],
  {
    // 对指定文件类型应用语言选项、插件和规则
    files: ['**/*.{js,mjs,cjs,ts,mts,cts,vue}'],
    languageOptions: {
      // 指定解析器
      // 'parserVue' 用于解析 .vue 文件，使其能够理解 <template>, <script>, <style> 块。
      parser: parserVue,
      parserOptions: {
        // 为 <script> 块指定解析器
        // 'tseslint.parser' 用于解析 TypeScript 代码。
        parser: tseslint.parser,
        sourceType: 'module', // 使用 ES 模块
        ecmaFeatures: {
          jsx: true // 启用 JSX 支持
        }
      },
      // 定义全局变量
      // 'globals' 用于声明在代码中可以使用的全局变量，避免 'no-undef' 错误。
      globals: {
        ...globals.browser, // 浏览器环境的全局变量
        ...globals.node, // Node.js 环境的全局变量
        ...globals.jest, // Jest 测试环境的全局变量
        ...globals.es2021, // ES2021 的全局变量
        // Vue 3 Composition API 的编译器宏，设为 'readonly' 表示它们是只读的全局变量。
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineOptions: 'readonly'
      }
    },
    // 注册插件
    plugins: {
      '@typescript-eslint': tseslint.plugin, // TypeScript 插件
      'vue-scoped-css': vueScopedCss, // Vue Scoped CSS 插件
      prettier: pluginPrettier
    },
    // 解析器设置
    settings: {
      // 'import/extensions' 用于 import/resolver，指定在解析模块时可以省略哪些文件扩展名。
      'import/extensions': ['.js', '.jsx', '.ts', '.tsx']
    },
    // 自定义规则
    rules: {
      'prettier/prettier': 'error',
      // --- 通用 JavaScript 规则 ---
      'no-console': 'off', // 允许使用 console
      'no-continue': 'off', // 允许使用 continue
      'no-restricted-syntax': 'off', // 不限制特定的语法
      'no-plusplus': 'off', // 允许使用 ++
      'no-param-reassign': 'off', // 允许对函数参数进行重新赋值
      'no-shadow': 'off', // 允许变量声明与外部作用域的变量同名
      'guard-for-in': 'off', // 允许在 for...in 循环中不使用 if 语句进行过滤

      // --- import 插件规则 ---
      'import/extensions': 'off', // 允许在 import 语句中不写文件扩展名
      'import/no-unresolved': 'off', // 关闭对未解析模块的检查（由 TypeScript 处理）
      'import/no-extraneous-dependencies': 'off', // 允许导入 package.json 中未声明的依赖
      'import/prefer-default-export': 'off', // 不强制使用默认导出
      'import/first': 'off', // 不强制 import 语句在文件顶部

      // --- TypeScript 插件规则 ---
      '@typescript-eslint/no-explicit-any': 'off', // 允许使用 any 类型
      '@typescript-eslint/explicit-module-boundary-types': 'off', // 不强制导出函数和类的公共方法的返回类型
      'vue/first-attribute-linebreak': 'off', // 关闭对第一个属性换行的强制要求

      // --- 未使用变量规则 ---
      // 对未使用的变量发出警告，但忽略以下划线开头的变量。
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_'
        }
      ],
      'no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_'
        }
      ],

      // --- 其他规则 ---
      'no-use-before-define': 'off', // 关闭 '在使用前定义' 的规则（由 TypeScript 插件处理）
      '@typescript-eslint/no-use-before-define': 'off', // 关闭 TypeScript 版本的 '在使用前定义'
      '@typescript-eslint/ban-ts-comment': 'off', // 允许使用 @ts-comment 注释
      '@typescript-eslint/ban-types': 'off', // 允许使用 Function, Object 等类型
      'class-methods-use-this': 'off', // 允许类方法不使用 this
      'vue/multi-word-component-names': 'off' // 允许组件名是单个单词
    }
  },
  {
    // 针对 .vue 文件的特定规则覆盖
    files: ['*.vue'],
    rules: {
      // 在模板中强制组件名称使用 kebab-case 写法
      'vue/component-name-in-template-casing': ['error', 'kebab-case'],
      // 不强制 props 有默认值
      'vue/require-default-prop': 'off',
      // 允许组件名是单个单词
      'vue/multi-word-component-names': 'off',
      // 允许使用 Vue 保留的 props 名称
      'vue/no-reserved-props': 'off',
      // 允许使用 v-html
      'vue/no-v-html': 'off',
      // 强制 <style> 标签使用 'scoped' 属性
      'vue-scoped-css/enforce-style-type': ['error', { allows: ['scoped'] }]
    }
  },
  {
    // 针对 .ts 和 .tsx 文件的特定规则覆盖
    // 这些规则通常与 TypeScript 编译器的检查重叠，因此在这里被关闭以避免冲突。
    files: ['*.ts', '*.tsx'],
    rules: {
      'constructor-super': 'off', // ts(2335) & ts(2377)
      'getter-return': 'off', // ts(2378)
      'no-const-assign': 'off', // ts(2588)
      'no-dupe-args': 'off', // ts(2300)
      'no-dupe-class-members': 'off', // ts(2393) & ts(2300)
      'no-dupe-keys': 'off', // ts(1117)
      'no-func-assign': 'off', // ts(2539)
      'no-import-assign': 'off', // ts(2539) & ts(2540)
      'no-new-symbol': 'off', // ts(2588)
      'no-obj-calls': 'off', // ts(2349)
      'no-redeclare': 'off', // ts(2451)
      'no-setter-return': 'off', // ts(2408)
      'no-this-before-super': 'off', // ts(2376)
      'no-undef': 'off', // ts(2304)
      'no-unreachable': 'off', // ts(7027)
      'no-unsafe-negation': 'off', // ts(2365) & ts(2361)
      'no-var': 'error', // ts transpiles let/const to var, so no need for vars
      'prefer-const': 'error', // ts provides better types with const
      'prefer-rest-params': 'error', // ts provides better types with rest args over arguments
      'prefer-spread': 'error', // ts transpiles spread to apply, so no need for manual apply
      'valid-typeof': 'off' // ts(2367)
    }
  },
  prettierConfig
);
