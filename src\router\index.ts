import { createRouter, createWebHistory } from 'vue-router';
import { keepAliveChange } from '../utils/keep-alive';
import { useTitle } from '@vueuse/core';

import { loginRoutes } from './modules/login';
import { developRoutes } from './modules/develop';
import { mainRoutes } from './modules/main';
import { useUserStore } from '@/store';
import { secondScreenRoutes } from './modules/second-screen';

// 缓存路由控制, 键名为 from.name ( 即路由name 及 组件 name, 请保持组件名称与路由名称一致), 值为到达路由需要缓存
const keepAliveConfig = {
  //Calculation: ['*'] // Calculation 路由需要缓存所有
  //LoginIndex: ['*']
};

// 定义路由配置
const routes = [
  ...loginRoutes,
  ...developRoutes,
  ...mainRoutes,
  ...secondScreenRoutes,
  {
    path: '/:w+',
    name: '404Page',
    component: () => import('@/views/404.vue')
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes // 使用定义的路由配置
});

// 定义路由前置守卫
router.beforeEach((to, from, next) => {
  // 处理缓存的路由
  keepAliveChange(keepAliveConfig, to, from);

  // 处理登录状态
  const userStore = useUserStore();

  // 处理公共路由
  if (to.meta.public) {
    next();
  } else if (!userStore.token && to.name !== 'LoginIndex') {
    next({ name: 'LoginIndex' });
  } else {
    next();
  }

  // 设置页面标题
  useTitle(to.meta.title as string);
});

// 导出 router
export default router;
