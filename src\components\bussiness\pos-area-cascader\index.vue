<template>
  <t-cascader
    v-model="cascaderValue"
    v-bind="$attrs"
    :options="areaList"
    :placeholder="$t('component.posAreaCascader.placeholder')"
    filterable
    clearable
    value-type="full"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { areaList as rawAreaList } from '@/constants/area';

// 定义地区数据项的类型
interface AreaItem {
  label: string;
  value: string;
  children?: AreaItem[];
}

// 处理后的地区数据项类型
interface ProcessedAreaItem {
  label: string;
  value: string;
  originalValue: string;
  children?: ProcessedAreaItem[];
}

// TDesign Cascader 变化事件的上下文类型
interface CascaderChangeContext {
  node: ProcessedAreaItem;
  source: 'click' | 'clear' | 'uncheck';
}

// Props 定义
interface Props {
  modelValue?: string[] | string[][];
  multiple?: boolean;
}

// Emits 定义
interface Emits {
  'update:modelValue': [value: string[] | string[][]];
  change: [value: string[] | string[][], context: CascaderChangeContext];
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => []
});

const emit = defineEmits<Emits>();

// 启用属性透传
defineOptions({
  inheritAttrs: false
});

// 处理重复值的函数，生成唯一的 value
function processAreaData(data: AreaItem[], parentPath = ''): ProcessedAreaItem[] {
  return data.map((item, index) => {
    // 生成唯一的路径作为 value
    const uniqueValue = parentPath ? `${parentPath}-${index}` : `${index}`;

    const processedItem: ProcessedAreaItem = {
      ...item,
      value: uniqueValue,
      // 保留原始值用于显示和逻辑处理
      originalValue: item.value,
      children: item.children ? processAreaData(item.children, uniqueValue) : undefined
    };

    return processedItem;
  });
}

// 导出处理后的数据，确保所有 value 唯一
const areaList = processAreaData(rawAreaList);

// 根据原始值路径查找对应的唯一 value
function findValueByPath(data: ProcessedAreaItem[], path: string[]): string[] {
  const result: string[] = [];
  let currentLevel = data;

  for (let i = 0; i < path.length; i++) {
    const target = path[i];
    const found = currentLevel.find(item => item.originalValue === target);
    if (found) {
      result.push(found.value);
      currentLevel = found.children || [];
    } else {
      break;
    }
  }

  return result;
}

// 根据唯一 value 路径查找对应的原始值
function findPathByValue(data: ProcessedAreaItem[], values: string[]): string[] {
  const result: string[] = [];
  let currentLevel = data;

  for (let i = 0; i < values.length; i++) {
    const targetValue = values[i];
    const found = currentLevel.find(item => item.value === targetValue);
    if (found) {
      result.push(found.originalValue);
      currentLevel = found.children || [];
    } else {
      break;
    }
  }

  return result;
}

// 双向绑定的计算属性
const cascaderValue = computed({
  get: () => {
    if (!props.modelValue || (Array.isArray(props.modelValue) && props.modelValue.length === 0)) {
      return [];
    }

    if (props.multiple) {
      // 多选模式：处理二维数组
      const multipleValue = props.modelValue as string[][];
      return multipleValue.map(path => findValueByPath(areaList, path));
    } else {
      // 单选模式：处理一维数组
      const singleValue = props.modelValue as string[];
      return findValueByPath(areaList, singleValue);
    }
  },
  set: value => {
    if (props.multiple) {
      // 多选模式：转换二维数组
      const multipleValue = value as string[][];
      const originalPaths = multipleValue.map(path => findPathByValue(areaList, path));
      emit('update:modelValue', originalPaths);
    } else {
      // 单选模式：转换一维数组
      const singleValue = value as string[];
      const originalPath = findPathByValue(areaList, singleValue);
      emit('update:modelValue', originalPath);
    }
  }
});

// 处理变化事件
const handleChange = (value: string[] | string[][], context: CascaderChangeContext) => {
  if (props.multiple) {
    // 多选模式：转换二维数组
    const multipleValue = value as string[][];
    const originalPaths = multipleValue.map(path => findPathByValue(areaList, path));
    emit('update:modelValue', originalPaths);
    emit('change', originalPaths, context);
  } else {
    // 单选模式：转换一维数组
    const singleValue = value as string[];
    const originalPath = findPathByValue(areaList, singleValue);
    emit('update:modelValue', originalPath);
    emit('change', originalPath, context);
  }
};
</script>
