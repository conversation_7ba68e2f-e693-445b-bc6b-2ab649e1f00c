@font-face {
  font-family: 'HarmonyOS_Sans_SC';
  src: url('@/src/assets/font/HarmonyOS_Sans_SC/HarmonyOS_Sans_SC_Medium.ttf');
}

:root {
  --td-font-family: HarmonyOS_Sans_SC; 
  --td-font-family-medium: HarmonyOS_Sans_SC;

  // 字体大小 token 适用与font-size属性
  --td-font-size-link-small: 12px;
  --td-font-size-link-medium: 14px;
  --td-font-size-link-large: 16px;
  --td-font-size-mark-small: 12px;
  --td-font-size-mark-medium: 14px;
  --td-font-size-body-small: 12px;
  --td-font-size-body-medium: 14px;
  --td-font-size-body-large: 16px;
  --td-font-size-title-small: 14px;
  --td-font-size-title-medium: 16px;
  --td-font-size-title-large: 20px;
  --td-font-size-headline-small: 24px;
  --td-font-size-headline-medium: 28px;
  --td-font-size-headline-large: 36px;
  --td-font-size-display-medium: 48px;
  --td-font-size-display-large: 64px;

  // 行高 token 适应于 line-height 属性
  --td-line-height-link-small: 20px;
  --td-line-height-link-medium: 22px;
  --td-line-height-link-large: 24px;
  --td-line-height-mark-small: 20px;
  --td-line-height-mark-medium: 22px;
  --td-line-height-body-small: 20px;
  --td-line-height-body-medium: 22px;
  --td-line-height-body-large: 24px;
  --td-line-height-title-small: 22px;
  --td-line-height-title-medium: 24px;
  --td-line-height-title-large: 28px;
  --td-line-height-headline-small: 32px;
  --td-line-height-headline-medium: 36px;
  --td-line-height-headline-large: 44px;
  --td-line-height-display-medium: 56px;
  --td-line-height-display-large: 72px;

  // 字体 token 集合font-size与line-height 适应于 font 属性 推荐使用
  --td-font-link-small: var(--td-font-size-link-small) / var(--td-line-height-link-small) var(--td-font-family);
  --td-font-link-medium: var(--td-font-size-link-medium) / var(--td-line-height-link-medium) var(--td-font-family);
  --td-font-link-large: var(--td-font-size-link-large) / var(--td-line-height-link-large) var(--td-font-family);
  --td-font-mark-small: 600 var(--td-font-size-mark-small) / var(--td-line-height-mark-small) var(--td-font-family);
  --td-font-mark-medium: 600 var(--td-font-size-mark-medium) / var(--td-line-height-mark-medium) var(--td-font-family);
  --td-font-body-small: var(--td-font-size-body-small) / var(--td-line-height-body-small) var(--td-font-family);
  --td-font-body-medium: var(--td-font-size-body-medium) / var(--td-line-height-body-medium) var(--td-font-family);
  // --td-font-body-large: var(--td-font-size-body-large) / var(--td-line-height-body-large) var(--td-font-family);
  --td-font-body-large: var(--td-font-size-body-medium) / var(--td-line-height-body-medium) var(--td-font-family);
  --td-font-title-small: 600 var(--td-font-size-title-small) / var(--td-line-height-title-small) var(--td-font-family);
  --td-font-title-medium: 600 var(--td-font-size-title-medium) / var(--td-line-height-title-medium) var(--td-font-family);
  --td-font-title-large: 600 var(--td-font-size-title-large) / var(--td-line-height-title-large) var(--td-font-family);
  --td-font-headline-small: 600 var(--td-font-size-headline-small) / var(--td-line-height-headline-small) var(--td-font-family);
  --td-font-headline-medium: 600 var(--td-font-size-headline-medium) / var(--td-line-height-headline-medium) var(--td-font-family);
  --td-font-headline-large: 600 var(--td-font-size-headline-large) / var(--td-line-height-headline-large) var(--td-font-family);
  --td-font-display-medium: 600 var(--td-font-size-display-medium) / var(--td-line-height-display-medium) var(--td-font-family);
  --td-font-display-large: 600 var(--td-font-size-display-large) / var(--td-line-height-display-large) var(--td-font-family);
}