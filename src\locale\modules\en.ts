export default {
  component: {
    cancel: 'Cancel',
    action: 'Confirm',
    save: 'Save',
    edit: 'Edit',
    search: 'Search',
    posAreaCascader: {
      placeholder: 'Please select province/city/district',
      selectArea: 'Please select your area',
      defaultDemo: 'Default Configuration',
      multipleDemo: 'Multiple Selection Mode',
      sizeDemo: 'Different Sizes',
      smallSize: 'Small Size',
      mediumSize: 'Medium Size',
      largeSize: 'Large Size',
      selectedValue: 'Selected Value'
    }
  },
  message: {
    error: 'Internal Server Error',
    error0: 'Network Error',
    error400: 'Access Denied',
    error401: 'Unauthorized Access',
    error403: 'Bad Request',
    error404: 'Request Address Error',
    error408: 'Request Timeout',
    error500: 'Internal Server Error',
    error501: 'Service Not Implemented',
    error502: 'Gateway Error',
    error503: 'Service Unavailable',
    error504: 'Gateway Timeout',
    error505: 'HTTP Version Not Supported'
  },
  login: {
    title: 'Login to',
    haveAccount: 'Already have an account?',
    noAccount: "Don't have an account?",
    signIn: 'Sign In',
    registerNewAccount: 'Register New Account',
    copyright: 'Copyright © 2025 ChuangJi Software Co., Ltd.',

    form: {
      accountPlaceholder: 'Please enter account: admin',
      passwordPlaceholder: 'Please enter password: admin',
      rememberAccount: 'Remember Account',
      forgotAccount: 'Forgot Account?',
      wechatScan: 'Please use WeChat to scan and login',
      refresh: 'Refresh',
      phonePlaceholder: 'Please enter phone number',
      verifyCodePlaceholder: 'Please enter verification code',
      sendVerifyCode: 'Send Verification Code',
      resendAfter: 'seconds to resend',
      loginButton: 'Login',
      useAccountPassword: 'Login with Account Password',
      useWechatScan: 'Login with WeChat Scan',
      usePhone: 'Login with Phone Number'
    },

    register: {
      phonePlaceholder: 'Please enter your phone number',
      emailPlaceholder: 'Please enter your email',
      passwordPlaceholder: 'Please enter password',
      haveReadAndAgree: 'I have read and agree to',
      serviceAgreement: 'TDesign Service Agreement',
      privacyStatement: 'TDesign Privacy Statement',
      registerButton: 'Register',
      useEmail: 'Register with Email',
      usePhone: 'Register with Phone Number'
    },

    validation: {
      phoneRequired: 'Phone number is required',
      accountRequired: 'Account is required',
      passwordRequired: 'Password is required',
      verifyCodeRequired: 'Verification code is required',
      emailRequired: 'Email is required',
      emailInvalid: 'Please enter a valid email'
    },

    message: {
      loginSuccess: 'Login successful',
      registerSuccess: 'Registration successful',
      mustAgree: 'Please agree to TDesign Service Agreement and TDesign Privacy Statement'
    }
  },
  develop: {
    develop: 'Development'
  },
  app: {
    theme: 'Theme',
    component: 'Component',
    language: 'Language'
  },
  customerService: {
    contact: 'Contact Support',
    title: 'Dedicated After-sales Service',
    description: '"Product usage questions, troubleshooting, I will do my best to solve them for you."',
    wechatTip: 'Open WeChat "Scan" to add dedicated customer service',
    phoneNumber: '400-0330-2520 ext. 2',
    workTime: 'Customer service hours: 8:00-17:00',
    confirm: 'Got it'
  },
  result: {
    notFound: {
      title: '404 Not Found',
      tip: 'Sorry, the page you are looking for does not exist',
      backHome: 'Back to Home'
    }
  }
};
