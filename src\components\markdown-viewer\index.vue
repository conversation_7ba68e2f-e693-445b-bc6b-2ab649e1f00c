<template>
  <div class="markdown-viewer">
    <!-- Markdown 内容插槽 -->
    <slot name="markdown" />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';

/**
 * Markdown 查看器组件
 * 用于显示 markdown 内容并提供代码高亮功能
 */

/**
 * 组件挂载后执行代码高亮
 */
onMounted(() => {
  // 对所有代码块进行语法高亮
  hljs.highlightAll();
});
</script>

<style lang="less" scoped>
.markdown-viewer {
  /**
   * Markdown 内容样式定义
   * - 主要针对代码块的显示效果
   * - 使用深色主题配色方案
   */
  :deep(pre) {
    background-color: #333333;
    border-radius: 4px;
    padding: 10px;

    code {
      font-family:
        ui-monospace,
        SFMono-Regular,
        Menlo,
        Monaco,
        Consolas,
        Liberation Mono,
        Courier New,
        monospace;
    }
  }
}
</style>
