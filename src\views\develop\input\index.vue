<template>
  <div class="doc-demo">
    <div class="doc-card">
      <h3>Input 输入框组件使用示例</h3>

      <!-- 基础输入框 -->
      <div class="doc-section">
        <h4>基础输入框：</h4>
        <div class="doc-item">
          <span class="label">基础用法：</span>
          <t-input v-model="inputValue" placeholder="请输入内容" />
        </div>
      </div>

      <!-- 输入框状态 -->
      <div class="doc-section">
        <h4>输入框状态：</h4>
        <div class="doc-item">
          <span class="label">禁用状态：</span>
          <t-input v-model="inputValue" placeholder="禁用状态" disabled />
        </div>
      </div>

      <!-- 带提示信息 -->
      <div class="doc-section">
        <h4>带提示信息：</h4>
        <div class="doc-item">
          <span class="label">提示文字：</span>
          <t-input v-model="tipsValue" placeholder="请输入内容" tips="这是普通提示文本" />
        </div>
      </div>

      <!-- 错误状态 -->
      <div class="doc-section">
        <h4>错误状态：</h4>
        <div class="doc-item">
          <span class="label">错误提示：</span>
          <t-input v-model="errorValue" placeholder="请输入内容" status="error" tips="这是一个错误提示" />
        </div>
      </div>

      <!-- 输入框尺寸 -->
      <div class="doc-section">
        <h4>输入框尺寸：</h4>
        <div class="doc-item">
          <span class="label">不同尺寸：</span>
          <t-input size="small" placeholder="小尺寸" />
          <t-input size="medium" placeholder="中尺寸" />
          <t-input size="large" placeholder="大尺寸" />
        </div>
      </div>

      <!-- 可清除输入框 -->
      <div class="doc-section">
        <h4>可清除输入框：</h4>
        <div class="doc-item">
          <span class="label">带清除按钮：</span>
          <t-input v-model="clearableValue" placeholder="可清除内容" clearable />
        </div>
      </div>

      <!-- 密码输入框 -->
      <div class="doc-section">
        <h4>密码输入框：</h4>
        <div class="doc-item">
          <span class="label">密码模式：</span>
          <t-input v-model="passwordValue" type="password" placeholder="请输入密码" />
        </div>
      </div>

      <!-- 文本域 -->
      <div class="doc-section">
        <h4>文本域：</h4>
        <div class="doc-item">
          <span class="label">多行输入：</span>
          <t-input v-model="textareaValue" autosize placeholder="请输入多行文本" type="textarea" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineOptions({
  name: 'InputDemo'
});

const inputValue = ref('');
const clearableValue = ref('可清除的默认值');
const passwordValue = ref('password123');
const textareaValue = ref('');
const tipsValue = ref('');
const errorValue = ref('');
</script>

<style lang="less" scoped>
@import '@/style/example.less';
</style>
