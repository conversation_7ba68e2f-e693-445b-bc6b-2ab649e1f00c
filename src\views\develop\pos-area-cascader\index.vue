<template>
  <div class="doc-demo">
    <div class="doc-card">
      <h3>PosAreaCascader 省区市选择组件使用示例</h3>

      <!-- 默认配置 -->
      <div class="doc-section">
        <h4>{{ $t('component.posAreaCascader.defaultDemo') }}：</h4>
        <div class="doc-item">
          <span class="label">基础用法：</span>
          <pos-area-cascader v-model="basicValue" style="width: 300px" @change="handleBasicChange" />
          <p class="result">{{ $t('component.posAreaCascader.selectedValue') }}: {{ basicValue }}</p>
        </div>
      </div>

      <!-- 不同尺寸 -->
      <div class="doc-section">
        <h4>{{ $t('component.posAreaCascader.sizeDemo') }}：</h4>
        <div class="doc-item">
          <span class="label">{{ $t('component.posAreaCascader.smallSize') }}：</span>
          <pos-area-cascader v-model="sizeSmallValue" style="width: 200px" size="small" />
        </div>
        <div class="doc-item">
          <span class="label">{{ $t('component.posAreaCascader.mediumSize') }}：</span>
          <pos-area-cascader v-model="sizeMediumValue" style="width: 250px" size="medium" />
        </div>
        <div class="doc-item">
          <span class="label">{{ $t('component.posAreaCascader.largeSize') }}：</span>
          <pos-area-cascader v-model="sizeLargeValue" style="width: 300px" size="large" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { PosAreaCascader } from '@/components';

defineOptions({
  name: 'PosAreaCascaderDemo'
});

// 定义变化事件的上下文类型
interface CascaderChangeContext {
  node: {
    label: string;
    value: string;
    originalValue: string;
  };
  source: 'click' | 'clear' | 'uncheck';
}

// 默认配置
const basicValue = ref<string[]>([]);
const handleBasicChange = (value: string[] | string[][], context: CascaderChangeContext) => {
  console.log('基础选择变化:', value, context);
};

// 不同尺寸
const sizeSmallValue = ref<string[]>([]);
const sizeMediumValue = ref<string[]>([]);
const sizeLargeValue = ref<string[]>([]);
</script>

<style lang="less" scoped>
@import '@/style/example.less';
</style>
