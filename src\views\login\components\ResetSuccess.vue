<template>
  <div class="reset-success">
    <!-- 成功图标和标题 -->
    <div class="success-section">
      <!-- 成功图标 -->
      <div class="success-icon">
        <t-icon name="check-circle" class="check-icon" />
      </div>

      <!-- 标题 -->
      <h1 class="title">密码重置成功</h1>

      <!-- 描述 -->
      <p class="description">您的密码重置成功，点击下方即可快速登录</p>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <!-- 继续按钮 -->
      <t-button theme="primary" size="large" block @click="emit('continue')"> 继续 </t-button>

      <!-- 返回登录按钮 -->
      <button class="back-button" @click="emit('backToLogin')">
        <t-icon name="chevron-left" />
        <span>返回登录</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * @file 密码重置成功组件
 * @description 密码重置成功后的确认页面
 */

// 定义事件
const emit = defineEmits<{
  (_e: 'continue'): void;
  (_e: 'backToLogin'): void;
}>();

defineOptions({
  name: 'ResetSuccess'
});
</script>

<style scoped lang="less">
.reset-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  width: 320px;
}

.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 58px;
  height: 58px;
  margin-bottom: 12px;
}

.check-icon {
  width: 48px;
  height: 48px;
  color: var(--text-success-primary);
}

.title {
  margin: 0;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 700;
  font-size: 30px;
  line-height: 1.27;
  text-align: center;
  color: var(--text-primary);
}

.description {
  margin: 0;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.375;
  text-align: center;
  color: var(--text-secondary);
}

.action-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  background: transparent;
  padding: 0;
  cursor: pointer;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: var(--text-primary);
  transition: color 0.2s ease;
  margin: auto;

  &:hover {
    color: var(--text-secondary-hover);
  }
}

:deep(.t-button) {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
}
</style>
