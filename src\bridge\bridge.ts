import { uuid } from 'frontend-utils';
import type { JsonRpcRequest, JsonRpcResponse, JsonRpcNotification } from '@/bridge/types';
import { customReport } from 'frontend-tracking';

// --- 类型定义 ---
type PendingRequest = { resolve: (_value: any) => void; reject: (_reason?: any) => void; timeoutTimer: number };
export type EventListener = (_params: any) => void;

/**
 * 底层通信服务，处理与原生环境的 JSON-RPC 消息交换。
 * 这个类被设计为内部使用，业务代码不应直接与之交互。
 */
class BridgeService {
  private pendingRequests = new Map<string | number, PendingRequest>();
  private eventListeners = new Map<string, Set<EventListener>>();
  private readonly DEFAULT_TIMEOUT = 15000; // 15秒超时
  private platform: 'windows' | 'android' | 'unknown' = 'unknown';

  constructor() {
    this.detectPlatform();
    this.setupMessageListener();
  }

  // 1. 环境检测与监听设置
  private detectPlatform(): void {
    if (window.chrome?.webview) this.platform = 'windows';
    else if (window.AndroidBridge) this.platform = 'android';
    else {
      this.platform = 'unknown';
      this._logError('Bridge platform detection failed. No native bridge found.');
    }
  }

  private setupMessageListener(): void {
    if (this.platform === 'windows') {
      window.chrome!.webview!.addEventListener('message', event => this.handleNativeMessage(event.data));
    } else if (this.platform === 'android') {
      window.onNativeMessage = (message: string) => {
        try {
          this.handleNativeMessage(JSON.parse(message));
        } catch (e) {
          this._logError('Failed to parse message from Android', { originalMessage: message, error: e });
        }
      };
    }
  }

  // 2. 核心消息处理
  private handleNativeMessage(message: JsonRpcResponse | JsonRpcNotification): void {
    // Case A: 是一个响应 (有 id)
    if ('id' in message && message.id !== null) {
      const pending = this.pendingRequests.get(message.id);
      if (pending) {
        clearTimeout(pending.timeoutTimer);
        if ('result' in message) {
          pending.resolve(message.result);
        } else {
          this._logError('Received error response from native', { id: message.id, error: message.error });
          pending.reject(message.error);
        }
        this.pendingRequests.delete(message.id);
      }
    }
    // Case B: 是一个事件/通知 (没有 id)
    else if ('method' in message) {
      this.publish(message.method, message.params);
    }
  }

  // 3. 核心消息发送
  private postMessage(message: JsonRpcRequest | JsonRpcNotification): void {
    if (this.platform === 'windows') {
      window.chrome!.webview!.postMessage(message);
    } else if (this.platform === 'android') {
      window.AndroidBridge!.postMessage(JSON.stringify(message));
    } else {
      // 这个错误理论上在 detectPlatform 时已经记录，但作为防御性编程保留
      throw new Error('Native bridge is not available.');
    }
  }

  // 4. 暴露给上层 API 的公共方法
  public invoke<TResult = any, TParams = any>(
    method: string,
    params: TParams
  ): Promise<{ success: true; data: TResult } | { success: false; code?: number; message: string; data?: any }> {
    const id = uuid();
    const request: JsonRpcRequest<TParams> = { jsonrpc: '2.0', method, params, id };

    return new Promise(resolve => {
      const timeoutTimer = window.setTimeout(() => {
        this.pendingRequests.delete(id);
        const error = { code: -32000, message: `Request timed out after ${this.DEFAULT_TIMEOUT}ms` };
        this._logError(`Request timed out for method: ${method}`, { id, timeout: this.DEFAULT_TIMEOUT });
        resolve({ success: false, code: error.code, message: error.message });
      }, this.DEFAULT_TIMEOUT);

      this.pendingRequests.set(id, {
        resolve: (result: any) => resolve({ success: true, data: result }),
        reject: (error: any) =>
          resolve({
            success: false,
            code: error?.code || -32002,
            message: error?.message || 'Unknown error',
            data: error?.data
          }),
        timeoutTimer
      });

      try {
        this.postMessage(request);
      } catch (e) {
        clearTimeout(timeoutTimer);
        this.pendingRequests.delete(id);
        debugger;
        resolve({
          success: false,
          code: -1,
          message: this._formatError(e),
          data: e
        });
      }
    });
  }

  public notify<TParams = any>(
    method: string,
    params: TParams
  ): { success: true; data: null } | { success: false; code?: number; message: string; data?: any } {
    const notification: JsonRpcNotification<TParams> = { jsonrpc: '2.0', method, params };
    try {
      this.postMessage(notification);
      return { success: true, data: null };
    } catch (error) {
      // postMessage 已经记录了错误，这里不再重复记录
      // 返回统一的错误格式
      return {
        success: false,
        code: -1, // 统一使用发送消息失败的错误码
        message: this._formatError(error),
        data: error
      };
    }
  }

  public subscribe(eventName: string, listener: EventListener): () => void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, new Set());
    }
    this.eventListeners.get(eventName)!.add(listener);
    return () => {
      this.eventListeners.get(eventName)?.delete(listener);
    };
  }

  public off(eventName: string, listener?: EventListener): void {
    if (!listener) {
      // 如果没有指定监听器，移除该事件的所有监听器
      this.eventListeners.delete(eventName);
    } else {
      // 移除指定的监听器
      this.eventListeners.get(eventName)?.delete(listener);
      // 如果该事件没有监听器了，清理空的 Set
      if (this.eventListeners.get(eventName)?.size === 0) {
        this.eventListeners.delete(eventName);
      }
    }
  }

  private publish(eventName: string, data: any): void {
    this.eventListeners.get(eventName)?.forEach(listener => {
      try {
        listener(data);
      } catch (e) {
        this._logError(`Error in event listener for '${eventName}'`, { error: e });
      }
    });
  }

  // 5. 统一错误处理
  private _logError(summary: string, context: object = {}): void {
    console.error(`[BridgeService] ${summary}`, context);
    // 自定义log
    customReport({
      type: 'bridge-error',
      message: summary
    });
  }

  // 6. 错误格式化工具函数
  private _formatError(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'object' && error !== null) {
      const obj = error as any;
      if (obj.message) return obj.message;
      if (obj.code && obj.message) return `${obj.message} (code: ${obj.code})`;
      return JSON.stringify(error, null, 2);
    }
    return String(error);
  }
}

export default BridgeService;
