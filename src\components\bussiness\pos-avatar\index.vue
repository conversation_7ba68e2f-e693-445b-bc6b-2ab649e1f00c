<template>
  <div :class="['avatar', `avatar--${size}`, { 'avatar--focused': isFocused }]">
    <div class="avatar__wrapper">
      <div v-if="image" class="avatar__image" :style="{ backgroundImage: `url(${image})` }" />
      <div v-else class="avatar__text">
        {{ text }}
      </div>
    </div>
    <div v-if="statusIcon" :class="['avatar__status', `avatar__status--${statusIcon}`]">
      <div v-if="statusIcon === 'online'" class="avatar__status-indicator" />
      <div v-if="statusIcon === 'level'" class="avatar__status-level-icon" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { avatarProps } from './props';

defineProps(avatarProps);
</script>

<style lang="less" scoped>
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #414651;

  &--xs {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
  &--sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  &--md {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  &--lg {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }
  &--xl {
    width: 56px;
    height: 56px;
    font-size: 20px;
  }
  &--2xl {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }

  &--focused {
    .avatar__wrapper {
      box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.05);
    }
  }

  &__wrapper {
    width: 100%;
    height: 100%;
    border-radius: 9999px;
    background-color: #f5f5f5;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
  }

  &__text {
    line-height: 1;
  }

  &__status {
    position: absolute;
    bottom: 0;
    right: 0;
    border-radius: 9999px;

    &--online {
      .avatar--xs & {
        width: 6px;
        height: 6px;
      }
      .avatar--sm & {
        width: 8px;
        height: 8px;
      }
      .avatar--md & {
        width: 10px;
        height: 10px;
      }
      .avatar--lg & {
        width: 12px;
        height: 12px;
      }
      .avatar--xl & {
        width: 14px;
        height: 14px;
      }
      .avatar--2xl & {
        width: 16px;
        height: 16px;
      }
    }

    &--level {
      .avatar--xs & {
        width: 10px;
        height: 10px;
      }
      .avatar--sm & {
        width: 12px;
        height: 12px;
      }
      .avatar--md & {
        width: 14px;
        height: 14px;
      }
      .avatar--lg & {
        width: 16px;
        height: 16px;
      }
      .avatar--xl & {
        width: 18px;
        height: 18px;
      }
      .avatar--2xl & {
        width: 20px;
        height: 20px;
      }
    }
  }

  &__status-indicator {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #12b76a;
    border: 1.5px solid #fff;
  }

  &__status-level-icon {
    width: 100%;
    height: 100%;
    background-image: url(@/assets/level_icon.svg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
}
</style>
