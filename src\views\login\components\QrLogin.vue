<template>
  <div class="qr-login">
    <!-- 二维码容器 -->
    <div class="qr-container">
      <div class="qr-code">
        <!-- 这里将放置实际的二维码图片 -->
        <div class="qr-placeholder">
          <!-- 临时占位，实际使用时应该是二维码图片 -->
          <div class="qr-mock"></div>
        </div>
      </div>
    </div>

    <!-- 扫码提示文字 -->
    <p class="scan-tip">请使用"微信"扫一扫</p>

    <!-- 刷新按钮 -->
    <div class="refresh-section">
      <button class="refresh-button" @click="refreshQr">
        <t-icon name="refresh" class="refresh-icon" />
        <span>刷新</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * @file 扫码登录组件
 * @description 微信扫码登录组件，包含二维码显示和刷新功能
 */

// 刷新二维码
const refreshQr = () => {
  // TODO: 实现二维码刷新逻辑
  console.log('刷新二维码');
};

defineOptions({
  name: 'QrLogin'
});
</script>

<style scoped lang="less">
.qr-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.qr-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 200px;
}

.qr-code {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.qr-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
}

.qr-mock {
  width: 180px;
  height: 180px;
  background:
    linear-gradient(90deg, var(--text-primary) 50%, transparent 50%),
    linear-gradient(90deg, var(--text-primary) 50%, transparent 50%),
    linear-gradient(0deg, var(--text-primary) 50%, transparent 50%),
    linear-gradient(0deg, var(--text-primary) 50%, transparent 50%);
  background-size:
    20px 20px,
    20px 20px,
    20px 20px,
    20px 20px;
  background-position:
    0 0,
    0 10px,
    0 0,
    10px 0;
  border-radius: 4px;
  opacity: 0.6;
  position: relative;

  &::before {
    content: '二维码';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-primary);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
  }
}

.scan-tip {
  margin: 0;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.375;
  text-align: center;
  color: var(--text-secondary);
}

.refresh-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.375;
  color: var(--text-brand-tertiary);
  transition: color 0.2s ease;

  &:hover {
    color: var(--text-brand-secondary-hover);
  }
}

.refresh-icon {
  width: 20px;
  height: 20px;
  color: currentColor;
}
</style>
