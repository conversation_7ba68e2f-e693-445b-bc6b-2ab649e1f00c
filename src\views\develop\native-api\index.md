Native API 通信桥是 Web 前端与原生应用交互的核心模块，基于 JSON-RPC 2.0 协议，支持 Windows (WebView2) 和 Android 平台。

```typescript
const result = await nativeApi.request['printer.print'](params);
if (result.success) {
  console.log('成功:', result.data);
} else {
  console.error('失败:', result.message, result.code);
}
```

**响应格式:**

- 成功: `{ success: true, data: T }`
- 失败: `{ success: false, code?: number, message: string, data?: any }`

**适用范围:**

- `request` 方法: 返回 Promise，resolve 统一响应格式
- `notify` 方法: 直接返回统一响应格式

### 1. 导入 Native API

```typescript
import { CreateNativeApi } from '@/bridge';

// 创建 Native API 实例
const nativeApi = CreateNativeApi();
```

### 2. 基本使用

```vue
<script setup lang="ts">
import { CreateNativeApi } from '@/bridge';

// 创建实例
const nativeApi = CreateNativeApi();

// 使用三种通信模式
const example = async () => {
  // 模式1: 请求响应
  const result = await nativeApi.request['printer.print']({
    content: { text: 'Hello' },
    copies: 1
  });

  if (result.success) {
    console.log('打印成功:', result.data.jobId);
  } else {
    console.error('打印失败:', result.message, result.code);
  }

  // 模式2: 通知
  const notifyResult = nativeApi.notify['screen.update']({
    event: 'shopping'
  });

  if (notifyResult.success) {
    console.log('通知发送成功');
  } else {
    console.error('通知发送失败:', notifyResult.message);
    if (notifyResult.code) {
      console.error('错误码:', notifyResult.code);
    }
  }

  // 模式3: 事件监听
  const unsubscribe = nativeApi.on['screen.dataReceived'](data => {
    console.log('副屏数据:', data);
  });

  // : 移除监听
  nativeApi.off['screen.dataReceived']();
  // 或使用返回的取消函数
  unsubscribe();
};
</script>
```

## 📋 三种通信模式详解

### 模式1: 请求响应 (Request-Response)

Web 向原生发送请求并等待返回结果。适用于需要获取数据或确认操作结果的场景。

```typescript
// 打印机操作
const printResult = await nativeApi.request['printer.print']({
  content: {
    text: '销售小票内容',
    layout: 'receipt'
  },
  copies: 2
});

if (printResult.success) {
  console.log('打印任务ID:', printResult.data.jobId);
} else {
  console.error('打印失败:', printResult.message, '错误码:', printResult.code);
}

// 日志记录
const logResult = await nativeApi.request['logger.log']({
  logs: [
    {
      appId: 'POS-System',
      deviceKey: 'E4DD3241-2DE9-4DE8-BDF7-3E13E8D6AB91',
      id: `${new Date().toISOString().replace(/[:.]/g, '-')} ${Math.random().toString(36).substr(2, 6)}`,
      pageUrl: window.location.href,
      time: new Date().toISOString().slice(0, 19).replace('T', ' '),
      userId: 1001,
      userName: '张三',
      type: 'business-log',
      data: {
        info: 'user login success',
        module: 'auth',
        action: 'user-login'
      }
    }
  ]
});

if (logResult.success) {
  console.log('日志记录成功');
} else {
  console.error('日志记录失败:', logResult.message);
}
```

**错误处理:**

```typescript
// 新的错误处理机制：成功和失败都通过 resolve 返回
const result = await nativeApi.request['printer.print'](params);

if (result.success) {
  // 处理成功结果
  console.log('打印任务ID:', result.data.jobId);
} else {
  // 根据错误码进行不同处理
  switch (result.code) {
    case -32000:
      console.error('请求超时');
      break;
    default:
      console.error('其他错误:', result.message, result.data);
  }
}
```

### 模式2: 通知 (Notify)

Web 向原生发送通知。通知方法现在返回统一的响应格式，包含成功状态和错误信息。适用于状态同步、UI 更新等场景。

```typescript
// 更新副屏内容
const screenResult = nativeApi.notify['screen.update']({
  event: 'shopping'
});

if (screenResult.success) {
  console.log('副屏内容更新成功');
} else {
  console.error('副屏内容更新失败:', screenResult.message);
  if (screenResult.code) {
    console.error('错误码:', screenResult.code);
  }
}
```

### 模式3: 事件监听 (Event Subscription)

监听原生平台推送的事件。适用于硬件事件、状态变化等场景。

```typescript
// 监听副屏数据
const unsubscribeScreen = nativeApi.on['screen.dataReceived'](data => {
  console.log('事件类型:', data.event); // 'shopping' | 'other'
  console.log('数据内容:', data.data);

  // 处理不同类型的事件
  if (data.event === 'shopping') {
    handleShoppingEvent(data.data);
  } else {
    handleOtherEvent(data.data);
  }
});
```

### 移除监听器 (Remove Listeners)

移除事件监听器，防止内存泄漏。

```typescript
// 方法1: 使用返回的取消函数（推荐）
const unsubscribe = nativeApi.on['screen.dataReceived'](handler);
unsubscribe(); // 移除该监听器

// 方法2: 移除指定监听器
nativeApi.off['screen.dataReceived'](handler);

// 方法3: 移除所有监听器
nativeApi.off['screen.dataReceived'](); // 不传参数则移除所有
```

### 文件结构

```
src/
├── bridge/
│   ├── index.ts              # 主入口，导出 CreateNativeApi
│   ├── bridge.ts             # BridgeService 核心实现
│   └── types/
│       └── index.ts          # 完整的类型定义
└── views/develop/native-api/
    ├── index.vue             # 使用示例组件
    └── index.md              # 本文档
```
