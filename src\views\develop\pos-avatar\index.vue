<template>
  <div class="doc-demo">
    <div class="doc-card">
      <h3>PosAvatar 头像组件使用示例</h3>

      <!-- 头像尺寸 -->
      <div class="doc-section">
        <h4>头像尺寸：</h4>
        <div class="doc-item">
          <span class="label">不同尺寸：</span>
          <t-space>
            <pos-avatar size="xs" text="XS" />
            <pos-avatar size="sm" text="SM" />
            <pos-avatar size="md" text="MD" />
            <pos-avatar size="lg" text="LG" />
            <pos-avatar size="xl" text="XL" />
            <pos-avatar size="2xl" text="2XL" />
          </t-space>
        </div>
      </div>

      <!-- 文本头像 -->
      <div class="doc-section">
        <h4>文本头像：</h4>
        <div class="doc-item">
          <span class="label">显示文字：</span>
          <t-space>
            <pos-avatar size="lg" text="张" />
            <pos-avatar size="lg" text="李" />
            <pos-avatar size="lg" text="王" />
            <pos-avatar size="lg" text="赵" />
          </t-space>
        </div>
      </div>

      <!-- 图片头像 -->
      <div class="doc-section">
        <h4>图片头像：</h4>
        <div class="doc-item">
          <span class="label">显示图片：</span>
          <t-space>
            <pos-avatar size="lg" image="https://avatar.iran.liara.run/public/1" />
            <pos-avatar size="lg" image="https://avatar.iran.liara.run/public/2" />
            <pos-avatar size="lg" image="https://avatar.iran.liara.run/public/3" />
            <pos-avatar size="lg" image="https://avatar.iran.liara.run/public/4" />
          </t-space>
        </div>
      </div>

      <!-- 状态图标 -->
      <div class="doc-section">
        <h4>状态图标：</h4>
        <div class="doc-item">
          <span class="label">在线状态：</span>
          <t-space>
            <pos-avatar size="lg" text="在" status-icon="online" />
            <pos-avatar size="lg" image="https://avatar.iran.liara.run/public/5" status-icon="online" />
          </t-space>
        </div>
        <div class="doc-item">
          <span class="label">等级显示：</span>
          <t-space>
            <pos-avatar size="lg" text="等" status-icon="level" />
            <pos-avatar size="lg" image="https://avatar.iran.liara.run/public/6" status-icon="level" />
          </t-space>
        </div>
      </div>

      <!-- 聚焦状态 -->
      <div class="doc-section">
        <h4>聚焦状态：</h4>
        <div class="doc-item">
          <span class="label">选中效果：</span>
          <t-space>
            <pos-avatar size="lg" text="聚" is-focused />
            <pos-avatar size="lg" image="https://avatar.iran.liara.run/public/7" is-focused />
            <pos-avatar size="lg" text="焦" status-icon="online" is-focused />
            <pos-avatar size="lg" text="点" status-icon="level" is-focused />
          </t-space>
        </div>
      </div>

      <!-- 综合示例 -->
      <div class="doc-section">
        <h4>综合示例：</h4>
        <div class="doc-item">
          <span class="label">完整功能：</span>
          <t-space>
            <pos-avatar
              size="xl"
              :text="currentUser.name"
              :image="currentUser.avatar"
              :status-icon="currentUser.status"
              :is-focused="currentUser.focused"
              @click="handleAvatarClick"
            />
            <pos-avatar size="xl" text="客服" status-icon="online" @click="handleAvatarClick" />
          </t-space>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { PosAvatar } from '@/components';

defineOptions({
  name: 'PosAvatarDemo'
});

// 当前用户信息演示
const currentUser = ref({
  name: '张三',
  avatar: 'https://avatar.iran.liara.run/public/8',
  status: 'online' as const,
  focused: false
});

// 头像点击事件处理
const handleAvatarClick = () => {
  currentUser.value.focused = !currentUser.value.focused;
  console.log('头像被点击', currentUser.value);
};
</script>

<style lang="less" scoped>
@import '@/style/example.less';

// 头像组件特有的样式调整
.doc-item {
  display: flex;
  align-items: center;

  :deep(.pos-avatar) {
    margin-right: var(--td-comp-margin-s);
    margin-bottom: var(--td-comp-margin-xs);
  }
}
</style>
