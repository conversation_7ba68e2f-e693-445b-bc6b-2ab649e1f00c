{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["node", "vite/client"]}, "vueCompilerOptions": {"globalTypesPath": "src/types/globals.d.ts"}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "vite.config.ts"]}