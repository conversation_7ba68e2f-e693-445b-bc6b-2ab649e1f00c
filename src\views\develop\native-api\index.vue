<template>
  <div class="doc-demo">
    <div class="doc-card">
      <h3>原生通信桥 (Native API) 使用示例</h3>
      <p class="doc-description">
        基于 JSON-RPC 2.0 协议的 Web 与原生应用通信桥，支持三种通信模式。 所有 API 调用都采用统一的响应格式，通过
        <code>success</code> 字段区分成功和失败。
      </p>

      <!-- 模式1: 请求响应 -->
      <div class="doc-section">
        <h4>模式1: 请求响应 (Request-Response)</h4>
        <div class="doc-description">Web 向原生发送请求并等待返回结果。适用于需要获取数据或确认操作结果的场景。</div>
        <div class="doc-item">
          <div class="doc-controls">
            <t-button theme="primary" @click="testPrinterPrint"> 测试打印机 </t-button>
            <t-button @click="testLogger"> 测试日志记录 </t-button>
          </div>
          <div class="doc-result">
            <div v-if="requestResults.printer" class="result-item">
              <strong>打印结果:</strong>
              <pre>{{ JSON.stringify(requestResults.printer, null, 2) }}</pre>
            </div>
            <div v-if="requestResults.logger" class="result-item">
              <strong>日志结果:</strong>
              <pre>{{ JSON.stringify(requestResults.logger, null, 2) }}</pre>
            </div>
            <div v-if="requestError" class="result-item error">
              <strong>请求失败:</strong>
              <pre>{{ requestError }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 模式2: 通知 -->
      <div class="doc-section">
        <h4>模式2: 通知 (Notify)</h4>
        <div class="doc-description">Web 向原生发送通知，无需等待返回值。适用于状态同步、UI 更新等场景。</div>
        <div class="doc-item">
          <div class="doc-controls">
            <t-button theme="success" @click="updateScreenContent"> 更新副屏内容 </t-button>
            <t-button theme="warning" @click="testNotifyError"> 测试通知错误 </t-button>
          </div>
          <div class="doc-result">
            <div v-if="notifyResults.length" class="result-item">
              <strong>通知结果:</strong>
              <div class="notify-list">
                <div v-for="(result, index) in notifyResults" :key="index" class="notify-item">
                  <span class="notify-time">{{ result.time }}</span>
                  <span :class="['notify-status', result.success ? 'success' : 'error']">
                    {{ result.success ? '成功' : '失败' }}
                  </span>
                  <span class="notify-method">{{ result.method }}</span>
                  <pre v-if="!result.success" class="notify-error">{{ result.message }}</pre>
                </div>
              </div>
            </div>
            <div v-else class="info-text">暂无通知记录</div>
          </div>
        </div>
      </div>

      <!-- 模式3: 事件监听 -->
      <div class="doc-section">
        <h4>模式3: 事件监听 (Event Subscription)</h4>
        <div class="doc-description">监听原生平台推送的事件。适用于硬件事件、状态变化等场景。</div>
        <div class="doc-item">
          <div class="doc-controls">
            <t-button :theme="screenListening ? 'danger' : 'primary'" @click="toggleScreenListener">
              {{ screenListening ? '停止监听副屏数据' : '开始监听副屏数据' }}
            </t-button>
            <t-button @click="simulateScreenEvent"> 模拟副屏事件 </t-button>
          </div>
          <div class="doc-result">
            <div v-if="eventResults.length" class="event-log">
              <h5>接收到的事件:</h5>
              <div class="event-list">
                <div v-for="(event, index) in eventResults" :key="index" class="event-item">
                  <span class="event-time">{{ event.time }}</span>
                  <span class="event-name">{{ event.name }}</span>
                  <pre class="event-data">{{ JSON.stringify(event.data, null, 2) }}</pre>
                </div>
              </div>
            </div>
            <div v-else class="info-text">暂无事件接收</div>
          </div>
        </div>
      </div>

      <!-- 移除监听器 -->
      <div class="doc-section">
        <h4>移除监听器 (Remove Listeners)</h4>
        <div class="doc-description">移除事件监听器，防止内存泄漏。支持移除特定监听器或所有监听器。</div>
        <div class="doc-item">
          <div class="doc-controls">
            <t-button theme="warning" @click="removeAllListeners"> 移除所有监听器 </t-button>
            <t-button @click="clearEventLog"> 清空事件日志 </t-button>
          </div>
        </div>
      </div>

      <!-- 统一响应格式说明 -->
      <div class="doc-section">
        <h4>统一响应格式</h4>
        <div class="doc-description">自版本 2.0 起，所有 API 调用都采用统一的响应格式，不再抛出异常：</div>
        <div class="response-format">
          <div class="format-item">
            <span class="format-label success">成功响应:</span>
            <code>{ success: true, data: T }</code>
          </div>
          <div class="format-item">
            <span class="format-label error">失败响应:</span>
            <code>{ success: false, code?: number, message: string, data?: any }</code>
          </div>
        </div>
      </div>

      <!-- 代码示例 -->
      <div class="doc-section">
        <h4>代码示例：</h4>
        <markdown-viewer>
          <template #markdown>
            <markdown />
          </template>
        </markdown-viewer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { MarkdownViewer } from '@/components';
import { CreateNativeApi } from '@/bridge';
import markdown from './index.md';

defineOptions({
  name: 'NativeApiDemo'
});

// 创建 Native API 实例
const nativeApi = CreateNativeApi();

// 请求结果状态
const requestResults = ref<{
  printer?: any;
  logger?: any;
}>({});

const requestError = ref<string>('');

// 通知结果状态
const notifyResults = ref<
  Array<{
    time: string;
    method: string;
    success: boolean;
    message?: string;
  }>
>([]);

// 事件监听状态
const screenListening = ref(false);

// 事件结果
const eventResults = ref<
  Array<{
    time: string;
    name: string;
    data: any;
  }>
>([]);

// 用于存储取消订阅函数
const unsubscribeFunctions = ref<Array<() => void>>([]);

// 添加通知结果到记录
const addNotifyResult = (method: string, result: { success: boolean; message?: string }) => {
  notifyResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    method,
    success: result.success,
    message: result.message
  });

  // 限制记录条数
  if (notifyResults.value.length > 10) {
    notifyResults.value = notifyResults.value.slice(0, 10);
  }
};

// 模式1: 请求响应示例
const testPrinterPrint = async () => {
  requestError.value = '';
  const result = await nativeApi.request['printer.print']({
    content: { text: '测试打印内容', layout: 'receipt' },
    copies: 1
  });

  requestResults.value.printer = result;

  if (result.success) {
    MessagePlugin.success('打印请求发送成功');
  } else {
    requestError.value = `打印失败: ${result.message} (错误码: ${result.code})`;
    MessagePlugin.error('打印请求失败');
  }
};

const testLogger = async () => {
  requestError.value = '';
  const result = await nativeApi.request['logger.log']({
    logs: [
      {
        appId: 'POS-Demo',
        deviceKey: 'E4DD3241-2DE9-4DE8-BDF7-3E13E8D6AB91',
        id: `${new Date().toISOString().replace(/[:.]/g, '-')} ${Math.random().toString(36).substr(2, 6)}`,
        pageUrl: window.location.href,
        time: new Date().toISOString().slice(0, 19).replace('T', ' '),
        userId: 1,
        userName: '测试用户',
        type: 'business-log',
        data: {
          info: 'native-api-demo test action',
          module: 'native-api-demo',
          action: 'test-logger'
        }
      }
    ]
  });

  requestResults.value.logger = result;

  if (result.success) {
    MessagePlugin.success('日志记录成功');
  } else {
    requestError.value = `日志记录失败: ${result.message} (错误码: ${result.code})`;
    MessagePlugin.error('日志记录失败');
  }
};

// 模式2: 通知示例
const updateScreenContent = () => {
  const result = nativeApi.notify['screen.update']({
    event: 'shopping'
  });

  addNotifyResult('screen.update', result);

  if (result.success) {
    MessagePlugin.info('副屏内容更新通知已发送');
  } else {
    MessagePlugin.error(`副屏内容更新失败: ${result.message}`);
  }
};

// 测试通知错误的函数
const testNotifyError = () => {
  // 模拟一个会失败的通知调用，这里直接返回错误结果
  const result = {
    success: false,
    code: -32001,
    message: 'Native bridge is not available.',
    data: null
  } as const;

  addNotifyResult('test.error', result);

  if (result.success) {
    MessagePlugin.info('测试通知已发送');
  } else {
    MessagePlugin.error('测试错误捕获成功');
  }
};

// 模式3: 事件监听示例
const toggleScreenListener = () => {
  if (screenListening.value) {
    nativeApi.off['screen.dataReceived']();
    screenListening.value = false;
    MessagePlugin.info('已停止监听副屏数据');
  } else {
    const unsubscribe = nativeApi.on['screen.dataReceived'](data => {
      addEventResult('screen.dataReceived', data);
      MessagePlugin.success(`接收到副屏事件: ${data.event}`);
    });
    unsubscribeFunctions.value.push(unsubscribe);
    screenListening.value = true;
    MessagePlugin.info('开始监听副屏数据');
  }
};

// 模拟副屏事件，用于演示
const simulateScreenEvent = () => {
  // 在实际应用中，这个事件会由原生端推送
  // 这里仅用于演示效果
  if (screenListening.value) {
    addEventResult('screen.dataReceived', {
      event: 'shopping',
      data: {
        timestamp: Date.now(),
        source: 'web-simulation',
        content: '模拟的副屏数据'
      }
    });
    MessagePlugin.info('模拟副屏事件已触发');
  } else {
    MessagePlugin.warning('请先开始监听副屏数据');
  }
};

// 添加事件结果到日志
const addEventResult = (eventName: string, data: any) => {
  eventResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    name: eventName,
    data
  });

  // 限制日志条数
  if (eventResults.value.length > 10) {
    eventResults.value = eventResults.value.slice(0, 10);
  }
};

// 移除监听器示例
const removeAllListeners = () => {
  // 调用所有取消订阅函数
  unsubscribeFunctions.value.forEach(unsubscribe => unsubscribe());
  unsubscribeFunctions.value = [];

  // 重置状态
  screenListening.value = false;

  MessagePlugin.success('已移除所有监听器');
};

const clearEventLog = () => {
  eventResults.value = [];
  notifyResults.value = [];
  // 同时清空所有错误状态
  requestError.value = '';
  MessagePlugin.info('事件日志和通知记录已清空');
};

// 组件卸载时清理监听器
onUnmounted(() => {
  removeAllListeners();
});
</script>

<style lang="less" scoped>
@import '@/style/example.less';

.doc-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.doc-result {
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  padding: 16px;
  margin-top: 12px;
}

.result-item {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  &.error {
    color: var(--td-error-color);
  }

  strong {
    display: block;
    margin-bottom: 8px;
    color: var(--td-text-color-primary);
  }

  pre {
    background: var(--td-bg-color-code);
    padding: 8px;
    border-radius: var(--td-radius-small);
    font-size: 12px;
    overflow-x: auto;
  }
}

.info-text {
  color: var(--td-text-color-secondary);
  font-style: italic;
  margin: 0;
}

.event-log {
  h5 {
    margin: 0 0 12px 0;
    color: var(--td-text-color-primary);
  }
}

.event-list {
  max-height: 300px;
  overflow-y: auto;
}

.event-item {
  background: var(--td-bg-color-container-hover);
  border: 1px solid var(--td-border-level-2-color);
  border-radius: var(--td-radius-small);
  padding: 12px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.event-time {
  display: inline-block;
  background: var(--td-brand-color);
  color: white;
  padding: 2px 6px;
  border-radius: var(--td-radius-small);
  font-size: 12px;
  margin-right: 8px;
}

.event-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.event-data {
  background: var(--td-bg-color-code);
  padding: 8px;
  border-radius: var(--td-radius-small);
  font-size: 12px;
  margin-top: 8px;
  overflow-x: auto;
}

.error-hint {
  margin: 8px 0 0 0;
  padding: 8px;
  background: var(--td-warning-color-1);
  border: 1px solid var(--td-warning-color-3);
  border-radius: var(--td-radius-small);
  color: var(--td-warning-color-7);
  font-size: 12px;
}

.notify-list {
  max-height: 300px;
  overflow-y: auto;
}

.notify-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--td-bg-color-container-hover);
  border: 1px solid var(--td-border-level-2-color);
  border-radius: var(--td-radius-small);
  padding: 8px 12px;
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0;
  }
}

.notify-time {
  display: inline-block;
  background: var(--td-text-color-secondary);
  color: white;
  padding: 2px 6px;
  border-radius: var(--td-radius-small);
  font-size: 11px;
  white-space: nowrap;
}

.notify-status {
  padding: 2px 6px;
  border-radius: var(--td-radius-small);
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;

  &.success {
    background: var(--td-success-color-1);
    color: var(--td-success-color-7);
  }

  &.error {
    background: var(--td-error-color-1);
    color: var(--td-error-color-7);
  }
}

.notify-method {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--td-text-color-primary);
  font-weight: 500;
}

.notify-error {
  background: var(--td-error-color-1);
  color: var(--td-error-color-8);
  padding: 4px 6px;
  border-radius: var(--td-radius-small);
  font-size: 11px;
  margin: 0;
  flex: 1;
  min-width: 0;
}

.doc-description {
  color: var(--td-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;

  code {
    background: var(--td-bg-color-code);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }
}

.response-format {
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  padding: 16px;
  margin-top: 12px;
}

.format-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  code {
    background: var(--td-bg-color-code);
    padding: 4px 8px;
    border-radius: var(--td-radius-small);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    flex: 1;
  }
}

.format-label {
  padding: 2px 8px;
  border-radius: var(--td-radius-small);
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;

  &.success {
    background: var(--td-success-color-1);
    color: var(--td-success-color-7);
  }

  &.error {
    background: var(--td-error-color-1);
    color: var(--td-error-color-7);
  }
}
</style>
