# POS系统前端框架 (zgzn)

> 基于 Vue 3 + TypeScript + TDesign 的现代化混合架构 POS 系统前端解决方案

## 📋 项目概述

POS 系统前端（zgzn）是一个采用 **原生容器与 Web 核心相结合** 的混合式架构前端项目。通过轻量级 C# 原生应用作为宿主容器，承载高效的 Vue 3 Web 应用，实现了开发效率、用户体验与系统性能的最佳平衡。

### 🎯 项目目标

- **高效开发**: 充分利用 Web 前端生态系统，实现 UI 和业务逻辑的快速开发与迭代
- **卓越体验**: 借助原生能力，提供流畅的硬件交互和无缝的系统集成体验
- **平台兼容**: Web 核心确保 UI 与业务逻辑具备高度可移植性，可平滑迁移至未来的 Android 等平台
- **稳定可靠**: 原生容器为应用提供稳健的运行环境，并实现对系统资源的有效管理
- **类型安全**: 基于 TypeScript 和 JSON-RPC 2.0 的完整类型系统，确保开发时期的错误捕获
- **开发友好**: 提供完整的组件开发平台和调试环境

## 🚀 核心功能

### 🏗️ 混合式架构设计

- **原生容器层 (C#/.NET)**: 作为应用宿主环境，负责硬件抽象、系统能力封装和应用生命周期管理
- **Web 核心层 (Vue 3)**: 承载所有 UI 渲染和业务逻辑，提供声明式界面开发体验
- **通信桥接层 (JSBridge)**: 基于 JSON-RPC 2.0 协议的安全、高效、异步双向通信机制

### 🌉 原生桥接层 (JSBridge)

- **类型安全通信**: 完整的 TypeScript 类型定义，开发期错误捕获
- **三种通信模式**:
  - 请求-响应模式 (`request`): Web 调用原生并等待返回
  - 通知模式 (`notify`): Web 单向通知原生，无需响应
  - 事件订阅模式 (`on`): 监听原生主动推送的事件
- **多平台兼容**: 统一接口，同时支持 Windows WebView2 和 Android WebView
- **健壮性设计**: 超时控制、错误恢复、连接重试机制

### 🔧 硬件设备支持

- **扫码枪集成**: 原生层高精度输入捕获，区分手动输入与扫码输入
- **打印机接口**: 支持热敏小票打印机，统一的打印任务管理
- **外设抽象**: 硬件抽象层设计，便于集成更多 POS 设备

### 🧩 组件开发平台

- **开发页面**: `/develop` 路由提供完整的组件展示和测试环境
- **实时调试**: 与原生桥接层的实时交互调试
- **组件库**: 基于 TDesign 的业务组件扩展

### 🌍 国际化与主题

- **多语言支持**: 中英文双语，支持动态语言切换
- **响应式布局**: 多分辨率适配，基于 1366×768 基准设计
- **主题系统**: 完整的 CSS 变量主题体系，支持深色/浅色模式

### 📊 监控与分析

- **用户行为分析**: 集成百度统计，支持页面访问和事件追踪
- **日志系统**: 分层日志记录，支持本地文件和远程上报
- **性能监控**: 前端性能指标收集和分析

## 🏗️ 技术架构

### 整体架构图

```
    ┌─────────────────────────────────────────────────────────────┐
    │                      POS 终端系统                            │
    │  ┌─────────────────────────────────────────────────────────┐ │
    │  │              原生容器层 (C#/.NET)                        │ │
    │  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
    │  │  │ 应用生命周期 │  │ 硬件抽象层   │  │ 系统能力抽象 │    │ │
    │  │  │   管理      │  │ (HAL)      │  │            │    │ │
    │  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
    │  │                           │                           │ │
    │  │  ┌─────────────────────────┼─────────────────────────┐ │ │
    │  │  │             JSBridge (JSON-RPC 2.0)            │ │ │
    │  │  └─────────────────────────┼─────────────────────────┘ │ │
    │  └────────────────────────────┼───────────────────────────┘ │
    │                               │                             │
    │  ┌────────────────────────────┼───────────────────────────┐ │
    │  │              Web 核心层 (Vue 3)                        │ │
    │  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
    │  │  │ 视图层(UI)  │  │ 状态管理    │  │ 业务逻辑    │    │ │
    │  │  │ Components  │  │ (Pinia)     │  │ (Services)  │    │ │
    │  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
    │  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
    │  │  │ 路由管理    │  │ 国际化      │  │ HTTP 客户端  │    │ │
    │  │  │ (Router)    │  │ (i18n)      │  │ (Axios)     │    │ │
    │  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
    │  └─────────────────────────────────────────────────────────┘ │
    └─────────────────────────────────────────────────────────────┘
                                    │
                          ┌─────────┼─────────┐
                          │    后端 API 服务   │
                          └───────────────────┘
```

### 通信流程

```
Web 层 ──────┐
            │ 1. JSON-RPC Request
            ▼
       ┌─────────┐    2. 解析 & 验证    ┌─────────────┐
       │JSBridge │ ───────────────────► │ 原生 API    │
       │         │                     │ 处理器      │
       │         │ ◄─────────────────── │             │
       └─────────┘    3. 执行 & 响应    └─────────────┘
            │                               │
            │ 4. JSON-RPC Response          │ 硬件调用
            ▼                               ▼
Web 层 ──────┘                        ┌─────────────┐
                                      │ 硬件设备    │
                                      │ (打印机/扫码枪)│
                                      └─────────────┘
```

### 核心技术栈

#### 前端技术栈

| 技术                        | 版本    | 用途                  |
| --------------------------- | ------- | --------------------- |
| Vue                         | 3.5.18  | 渐进式前端框架        |
| TypeScript                  | 5.8.3   | 静态类型检查          |
| Vite                        | 7.0.6   | 现代化构建工具        |
| TDesign Vue Next            | latest  | 企业级 UI 组件库      |
| Pinia                       | 3.0.3   | 现代化状态管理        |
| Vue Router                  | 4.5.1   | 官方路由管理器        |
| Vue I18n                    | 11.1.11 | 国际化支持            |
| Axios                       | 1.11.0  | HTTP 请求库           |
| @vueuse/core                | 13.6.0  | Vue 组合式工具库      |
| pinia-plugin-persistedstate | 4.4.1   | Pinia 状态持久化      |
| frontend-tracking           | 1.2.3   | 前端埋点追踪          |
| frontend-utils              | 0.2.18  | 前端通用工具库        |
| dayjs                       | 1.11.13 | 轻量级日期处理        |
| lodash                      | 4.17.21 | JavaScript 实用工具库 |

#### 原生技术栈

| 技术             | 用途                      |
| ---------------- | ------------------------- |
| .NET 6/8         | 现代化跨平台开发框架      |
| WPF/WinForms     | Windows 桌面应用开发      |
| WebView2         | 基于 Chromium 的 Web 容器 |
| System.Text.Json | 高性能 JSON 序列化        |
| System.IO.Ports  | 串口通信（扫码枪等设备）  |

#### 通信协议

| 协议/标准    | 用途                   |
| ------------ | ---------------------- |
| JSON-RPC 2.0 | Web 与原生间标准化通信 |
| WebView2 API | 跨进程消息传递         |
| HTTP/REST    | 与后端服务通信         |

## 📁 项目结构

### Web 前端核心 (zgzn)

```
zgzn/
├── public/                          # 静态资源
│   ├── favicon.ico                  # 应用图标
│   └── tdesign-logo.svg             # TDesign 品牌图标
├── src/
│   ├── api/                         # 后端 API 接口层
│   │   ├── login.ts                 # 登录相关接口
│   │   └── types/                   # API 类型定义
│   │       └── login.ts             # 登录接口类型
│   ├── assets/                      # 静态资源文件
│   │   ├── assets-empty.svg         # 空状态图标
│   │   ├── assets-login-bg-*.png    # 登录背景图
│   │   ├── assets-logo-full.svg     # 完整 Logo
│   │   ├── assets-product-*.svg     # 产品相关图标
│   │   ├── assets-result-*.svg      # 结果页面图标
│   │   ├── assets-setting-*.svg     # 设置页面图标
│   │   ├── assets-slide-*.svg       # 导航图标
│   │   └── assets-tencent-logo.png  # 品牌 Logo
│   ├── bridge/                      # 🔥 原生桥接通信层
│   │   ├── bridge.ts                # 桥接服务核心实现
│   │   ├── index.ts                 # 类型安全的 API 工厂
│   │   └── types/                   # 桥接层完整类型定义
│   │       └── index.ts             # JSON-RPC 协议类型
│   ├── components/                  # 通用 UI 组件
│   │   ├── alive-router-view/       # 路由缓存组件
│   │   │   └── index.vue
│   │   ├── result/                  # 结果页组件
│   │   │   └── index.vue
│   │   └── index.ts                 # 组件统一导出
│   ├── composables/                 # Vue 组合式函数
│   │   ├── index.ts                 # composables 导出
│   │   └── modules/                 # 功能模块
│   │       └── use-counter.ts       # 计数器逻辑示例
│   ├── constants/                   # 全局常量定义
│   │   └── index.ts
│   ├── directives/                  # Vue 自定义指令
│   ├── layouts/                     # 页面布局组件
│   │   ├── index.ts                 # 布局组件导出
│   │   ├── layout-full-screen/      # 全屏布局
│   │   ├── layout-header-sidebar/   # 顶部+侧边栏布局
│   │   ├── layout-left-sidebar/     # 左侧边栏布局
│   │   ├── layout-right-sidebar/    # 右侧边栏布局
│   │   ├── layout-side-by-side/     # 并排布局
│   │   └── layout-top-down/         # 上下布局
│   ├── locale/                      # 国际化系统
│   │   ├── index.ts                 # i18n 配置
│   │   └── modules/                 # 语言包
│   │       ├── en.ts                # 英文语言包
│   │       └── zh-cn.ts             # 中文语言包
│   ├── router/                      # Vue Router 路由配置
│   │   ├── index.ts                 # 路由主配置文件
│   │   └── modules/                 # 路由模块
│   │       ├── develop.ts           # 🔥 开发调试页面路由
│   │       ├── login.ts             # 登录页面路由
│   │       └── main.ts              # 主应用路由
│   ├── store/                       # Pinia 状态管理
│   │   ├── index.ts                 # Pinia 实例配置
│   │   └── modules/                 # 状态模块
│   │       ├── config.ts            # 全局配置状态
│   │       └── user.ts              # 用户状态
│   ├── style/                       # 样式系统
│   │   ├── index.less               # 样式入口文件
│   │   ├── layout.less              # 布局相关样式
│   │   ├── reset.less               # CSS 重置
│   │   └── theme/                   # 主题系统
│   │       └── default/             # 默认主题
│   │           ├── border.less      # 边框样式变量
│   │           ├── color.less       # 颜色主题变量
│   │           ├── font.less        # 字体样式变量
│   │           ├── index.less       # 主题入口文件
│   │           ├── layout.less      # 布局样式变量
│   │           ├── shadow.less      # 阴影样式变量
│   │           └── size.less        # 尺寸样式变量
│   ├── types/                       # TypeScript 类型定义
│   │   ├── globals.d.ts             # 全局类型声明
│   │   └── interface.d.ts           # 通用接口定义
│   ├── utils/                       # 通用工具函数
│   │   ├── keep-alive.ts            # 路由缓存管理
│   │   ├── request.ts               # HTTP 请求封装
│   │   └── tracking.ts              # 用户行为追踪
│   ├── views/                       # 页面级组件
│   │   ├── 404.vue                  # 404 错误页面
│   │   ├── develop/                 # 🔥 开发调试页面
│   │   │   ├── button/              # 按钮组件示例
│   │   │   │   └── index.vue
│   │   │   ├── index.vue            # 开发页面主入口
│   │   │   └── index.less           # 开发页面样式
│   │   ├── login/                   # 登录模块
│   │   │   ├── components/          # 登录相关组件
│   │   │   │   ├── LoginForm.vue    # 登录表单
│   │   │   │   └── RegisterForm.vue # 注册表单
│   │   │   ├── index.vue            # 登录页面主入口
│   │   │   └── index.less           # 登录页面样式
│   │   └── main/                    # 主业务页面
│   │       ├── index.vue            # 主页面
│   │       └── index.less           # 主页面样式
│   ├── App.vue                      # Vue 应用根组件
│   └── main.ts                      # 应用程序入口文件
├── index.html                       # HTML 入口模板
├── package.json                     # 项目依赖配置
├── tsconfig.json                    # TypeScript 编译配置
├── vite.config.ts                   # Vite 构建配置
├── eslint.config.js                 # ESLint 代码规范配置
├── POS系统前端软件架构设计方案.md     # 📄 完整架构设计文档
└── README.md                        # 项目说明文档
```

### 项目特色目录说明

- **🔥 `/bridge`**: 核心桥接通信层，实现 Web 与 C# 原生应用的类型安全通信
- **🔥 `/develop`**: 组件开发和调试环境，实时预览和测试 POS 业务组件
- **🎨 `/layouts`**: 多种布局方案，适配不同 POS 设备屏幕和使用场景
- **🌍 `/locale`**: 完整的国际化支持，便于多地区部署
- **🎨 `/theme`**: CSS 变量驱动的主题系统，支持深色/浅色模式切换
- **📊 `/tracking`**: 用户行为分析和性能监控集成

## 🔧 核心模块详解

### 1. 桥接层 (Bridge Layer) - 核心通信枢纽

桥接层是整个混合架构的核心，实现了 Web 层与原生层之间的类型安全、高效通信。

#### 核心特性

- **JSON-RPC 2.0 协议**: 轻量级、无状态的标准化通信协议
- **三层架构设计**: 类型定义层 + 通信服务层 + API 接口层
- **类型安全**: 完整的 TypeScript 类型系统，开发期错误捕获
- **跨平台一致性**: 统一接口，支持 Windows WebView2 和 Android WebView
- **健壮性保障**: 超时控制、错误恢复、连接重试机制
- **契约驱动开发**: 所有通信接口在 `bridge/types` 中统一定义

#### 三种通信模式

```typescript
import { CreateNativeApi } from '@/bridge';
const nativeApi = CreateNativeApi();

// 模式1: 请求-响应 (Web 调用原生，等待返回)
const response = await nativeApi.request['printer.print']({
  content: { ticket: '...' },
  copies: 1
});

// 模式2: 通知 (Web 单向通知原生，无需等待)
nativeApi.notify['ui.showToast']({
  message: '操作成功',
  duration: 'short'
});

// 模式3: 事件监听 (监听原生主动推送的事件)
const unsubscribe = nativeApi.on['scanner.dataReceived'](data => {
  console.log('扫码结果:', data.code);
});
```

#### 标准化响应结构

所有 `request` 模式调用都返回统一的 `ApiResponse<T>` 结构：

```typescript
// 成功响应
interface SuccessResponse<T> {
  success: true;
  data: T;
}

// 失败响应
interface ErrorResponse {
  success: false;
  code: number; // 业务错误码
  message: string; // 错误描述
}
```

### 2. 开发页面 (Develop Page) - 组件开发平台

`/develop` 页面是专为 POS 业务组件开发设计的集成开发环境：

#### 核心功能

- **🧩 组件展示**: 实时预览正在开发的 POS 业务组件
- **🔧 交互测试**: 一键测试组件与原生硬件功能的交互
- **📋 API 调试**: 直接在页面上调试桥接层的各种 API
- **🎨 样式预览**: 多主题、多尺寸的组件样式预览
- **📊 性能监控**: 组件渲染性能和内存使用情况

#### 使用方式

```typescript
// 在 /views/develop/ 下添加新的组件测试
<template>
  <div class="develop-section">
    <h2>打印机组件测试</h2>
    <PrinterTestComponent @print="handlePrint" />
  </div>
</template>

<script setup lang="ts">
import { CreateNativeApi } from '@/bridge';

const nativeApi = CreateNativeApi();

const handlePrint = async (content: any) => {
  const result = await nativeApi.request['printer.print'](content);
  console.log('打印结果:', result);
};
</script>
```

### 3. 状态管理 (Pinia Store) - 响应式状态中心

基于 Pinia 的现代化状态管理，支持状态持久化和 TypeScript 类型安全：

#### 全局配置状态 (`config.ts`)

```typescript
import { useConfigStore } from '@/store/modules/config';

const configStore = useConfigStore();

// 应用状态
configStore.isLoading; // 全局加载状态
configStore.locale; // 当前语言 (zh-cn/en)
configStore.theme; // 主题模式 (light/dark/auto)

// 页面缓存
configStore.keepAliveList; // 需要缓存的路由列表
```

#### 用户状态 (`user.ts`)

```typescript
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

// 用户信息
userStore.permissions; // 用户权限列表
```

#### 状态持久化

关键状态通过 `pinia-plugin-persistedstate` 自动持久化到 `localStorage`：
并使用 `secure-ls` 加密存储。

```typescript
// 自动持久化配置
{
  persist: {
    storage,
    paths: ['locale', 'theme', 'keepAliveList']
  }
}
```

### 4. 网络请求 (HTTP Client) - 统一 API 通信

基于 Axios 的高度封装 HTTP 客户端，提供类型安全的 API 调用：

#### 核心特性

- **自动认证**: JWT Token 自动注入请求头
- **请求/响应拦截**: 统一的数据处理和错误捕获
- **类型安全**: 完整的请求/响应类型定义
- **错误重试**: 网络异常自动重试机制
- **国际化**: 错误信息多语言支持

#### 使用示例

```typescript
// API 接口定义 (src/api/login.ts)
import { request } from '@/utils/request';
import type { LoginRequest, LoginResponse } from '@/api/types/login';

export function postPasswordLogin(data: LoginRequest, options = {}) {
  return request<LoginResponse>({
    url: `/login/password`,
    method: 'POST',
    data: data,
    ...options
  });
}

// 在组件中使用
const handleLogin = async (credentials: LoginRequest) => {
  try {
    const response = await postPasswordLogin(credentials);
    configStore.setToken(response.data.token);
  } catch (error) {
    // 错误处理
    console.error('登录失败:', error.message);
  }
};
```

### 5. 响应式布局系统

基于 CSS 变量的响应式设计，支持多分辨率 POS 设备：

#### 设计原则

- **基准分辨率**: 以 1366×768 为基础设计

## 🚀 快速开始

### 环境要求

| 环境           | 版本要求  | 说明              |
| -------------- | --------- | ----------------- |
| **Node.js**    | >= 22.0.0 | 推荐使用 LTS 版本 |
| **npm**        | >= 8.0.0  | 包管理器          |
| **TypeScript** | >= 5.0.0  | 已包含在依赖中    |
| **Git**        | >= 2.0.0  | 版本控制          |

### 克隆项目

```bash
git clone <repository-url>
cd zgzn
```

### 安装依赖

```bash
npm install
```

### 环境配置

根据需要创建环境配置文件：

```bash
# 开发环境 (可选，已有默认配置)
cp .env.example .env.development

# 生产环境 (部署时配置)
cp .env.example .env.production
```

### 启动开发服务器

```bash
npm run dev
```

服务器将在 `http://localhost:8000` 启动，并自动打开浏览器。

### 开发调试

1. **访问开发页面**: 导航到 `/develop` 查看组件展示和 API 调试界面
2. **实时热重载**: 修改代码后浏览器自动刷新
3. **类型检查**: TypeScript 提供实时类型检查
4. **DevTools**: 使用 Vue DevTools 进行状态调试

### 构建命令

```bash
# 构建生产版本
npm run build

# 类型检查
npm run build  # 包含 vue-tsc 类型检查

# 预览构建结果
npm run preview
```

### 代码质量

```bash
# ESLint 检查
npm run lint

# 自动修复代码风格问题
npm run lint:fix
```

### 项目脚本说明

| 命令               | 功能           | 说明                     |
| ------------------ | -------------- | ------------------------ |
| `npm run dev`      | 启动开发服务器 | 热重载, 端口 8000        |
| `npm run build`    | 构建生产版本   | 包含类型检查和代码压缩   |
| `npm run preview`  | 预览构建结果   | 本地预览生产构建         |
| `npm run lint`     | 代码检查       | ESLint 静态代码分析      |
| `npm run lint:fix` | 自动修复       | 修复可自动修复的代码问题 |
| `npm run prepare`  | Git Hook 准备  | Husky Git Hook 初始化    |

## 🎨 组件开发指南

### 1. 创建新组件

在 `src/components/` 目录下创建新的 POS 组件：

```vue
<template>
  <div class="pos-component">
    <t-card :title="title" class="component-card">
      <template #actions>
        <t-button @click="handleAction">{{ actionText }}</t-button>
      </template>

      <!-- 组件内容 -->
      <div class="component-content">
        <slot>默认内容</slot>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
// 类型定义
interface Props {
  title: string;
  actionText?: string;
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  actionText: '操作'
});

// Emits 定义
const emits = defineEmits<{
  action: [value: string];
}>();

// 组件选项
defineOptions({
  name: 'PosComponent'
});

// 组件逻辑
const handleAction = () => {
  emits('action', 'some-value');
};
</script>

<style lang="less" scoped>
.pos-component {
  .component-card {
    margin-bottom: 16px;

    .component-content {
      padding: 16px 0;
      min-height: 100px;
    }
  }
}
</style>
```

### 2. 在 Develop 页面测试组件

在 `src/views/develop/` 下创建组件测试页面：

```vue
<!-- src/views/develop/my-component/index.vue -->
<template>
  <div class="develop-section">
    <t-card title="POS组件测试" class="test-card">
      <div class="test-controls">
        <t-space>
          <t-input v-model="testTitle" placeholder="组件标题" />
          <t-button @click="resetComponent">重置</t-button>
        </t-space>
      </div>

      <!-- 测试组件实例 -->
      <div class="test-instance">
        <PosComponent :title="testTitle" action-text="测试操作" @action="handleComponentAction">
          <p>这是一个测试组件的内容区域</p>
        </PosComponent>
      </div>

      <!-- 测试结果展示 -->
      <div class="test-results">
        <h4>测试结果:</h4>
        <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PosComponent from '@/components/PosComponent.vue';

// 测试状态
const testTitle = ref('测试组件');
const testResults = ref<any[]>([]);

// 处理组件事件
const handleComponentAction = (value: string) => {
  testResults.value.push({
    timestamp: new Date().toISOString(),
    action: 'component-action',
    value
  });
};

// 重置组件状态
const resetComponent = () => {
  testTitle.value = '测试组件';
  testResults.value = [];
};
</script>

<style lang="less" scoped>
.develop-section {
  .test-card {
    margin-bottom: 24px;
  }

  .test-controls {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--td-bg-color-container-select);
    border-radius: 8px;
  }

  .test-instance {
    margin: 16px 0;
    padding: 16px;
    border: 1px dashed var(--td-border-level-2-color);
    border-radius: 8px;
  }

  .test-results {
    padding: 16px;
    background: var(--td-bg-color-code);
    border-radius: 8px;

    pre {
      margin: 8px 0 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: var(--td-text-color-primary);
    }
  }
}
</style>
```

### 3. 使用原生桥接功能

```vue
<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import { CreateNativeApi } from '@/bridge';

// 创建桥接 API 实例
const nativeApi = CreateNativeApi();

// 组件状态
const isPrinting = ref(false);
const scannerData = ref<string[]>([]);

// 打印功能
const handlePrint = async () => {
  if (isPrinting.value) return;

  isPrinting.value = true;
  try {
    const response = await nativeApi.request['printer.print']({
      content: {
        header: 'POS 系统',
        items: [{ name: '测试商品', price: 10.0, qty: 1 }],
        total: 10.0
      },
      copies: 1
    });

    if (response.success) {
      // 打印成功，显示提示
      nativeApi.notify['ui.showToast']({
        message: `打印成功，任务ID: ${response.data.jobId}`,
        duration: 'long'
      });
    } else {
      console.error('打印业务失败:', response.message);
    }
  } catch (error) {
    console.error('打印调用失败:', error);
  } finally {
    isPrinting.value = false;
  }
};

// 扫码事件处理
let unsubscribeScanner: (() => void) | null = null;

onMounted(() => {
  // 监听扫码枪事件
  unsubscribeScanner = nativeApi.on['scanner.dataReceived'](data => {
    scannerData.value.push(`${data.type}: ${data.code}`);

    // 保持最近10条记录
    if (scannerData.value.length > 10) {
      scannerData.value = scannerData.value.slice(-10);
    }
  });

  // 监听网络状态变化
  nativeApi.on['network.statusChanged'](status => {
    console.log('网络状态变化:', status.online ? '在线' : '离线');
  });
});

// 清理资源
onUnmounted(() => {
  if (unsubscribeScanner) {
    unsubscribeScanner();
  }
});

// 记录日志
const logAction = async (action: string, context?: any) => {
  try {
    await nativeApi.request['logger.log']({
      context: {
        action,
        timestamp: Date.now(),
        context
      }
    });
  } catch (error) {
    console.error('日志记录失败:', error);
  }
};
</script>
```

## 🔒 环境配置

### 开发环境配置

当前项目通过 Vite 配置文件进行代理设置，无需额外的环境变量文件。

#### Vite 代理配置 (vite.config.ts)

```typescript
export default defineConfig({
  server: {
    port: 8000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://yapi.trechina.cn/mock/1535', // 开发环境API地址
        changeOrigin: true
      }
    }
  }
});
```

### 生产环境变量 (可选)

如需自定义环境变量，可创建以下文件：

#### .env.development

```env
# API 配置
VITE_API_URL=http://localhost:3000/api
VITE_API_TIMEOUT=10000

# 基础路径配置 (用于部署到子路径)
VITE_BASE_URL=./

```

#### .env.production

```env
# API 配置
VITE_API_URL=/api
VITE_API_TIMEOUT=15000

# 基础路径配置
VITE_BASE_URL=./
```

### 构建配置

#### TypeScript 配置 (tsconfig.json)

- 严格模式启用，确保类型安全
- 路径别名 `@` 指向 `src` 目录

#### ESLint 配置 (eslint.config.js)

- Vue 3 + TypeScript 规则
- Prettier 集成
- 自定义规则适配 POS 项目需求

## 📱 平台兼容性

### Windows WebView2

- ✅ **Chrome 内核**: 基于最新 Chromium，提供现代 Web 标准支持
- ✅ **完整 API 支持**: 支持 ES2020+、WebGL、Canvas 等现代 Web 技术
- ✅ **调试工具**: 集成 Chrome DevTools，支持断点调试
- ✅ **性能优化**: 硬件加速渲染，60fps 流畅体验
- ✅ **安全沙箱**: 进程隔离，确保应用稳定性

### Android WebView (规划中)

- ✅ **Android 7.0+**: 支持现代 Android 系统
- ✅ **Chromium 内核**: 与 Windows 版本保持一致的 Web 标准
- ✅ **原生桥接**: 统一的 JSBridge 接口，无缝迁移
- ✅ **硬件集成**: 支持 Android POS 设备的扫码、打印等功能

### 跨平台一致性

| 特性               | Windows WebView2 | Android WebView | 说明                 |
| ------------------ | ---------------- | --------------- | -------------------- |
| JavaScript ES2020+ | ✅               | ✅              | 完整支持现代 JS 语法 |
| CSS Grid/Flexbox   | ✅               | ✅              | 现代布局技术         |
| WebGL              | ✅               | ✅              | 3D 图形渲染          |
| LocalStorage       | ✅               | ✅              | 本地数据存储         |
| ServiceWorker      | ✅               | ✅              | 离线缓存支持         |
| JSBridge API       | ✅               | ✅              | 统一的原生通信接口   |
| 硬件抽象层         | ✅               | ✅              | 统一的硬件访问接口   |

## 🧪 测试策略

### 单元测试 (计划中)

- **组件测试**: 使用 Vue Test Utils 测试组件渲染和交互
- **工具函数测试**: Jest 单元测试覆盖工具函数
- **桥接层测试**: Mock 原生 API 进行桥接层功能测试
- **状态管理测试**: Pinia store 的 actions 和 getters 测试

### 集成测试 (计划中)

- **路由测试**: 页面间导航和权限控制测试
- **API 集成测试**: 与后端 API 的完整数据流测试
- **原生交互测试**: 模拟原生环境的桥接功能测试

### 端到端测试 (计划中)

- **业务流程测试**: 使用 Playwright 测试完整用户操作流程
- **跨平台测试**: Windows WebView2 和 Android WebView 兼容性测试
- **性能测试**: 页面加载速度和交互响应时间测试

## 📈 性能优化

### 构建优化

- **Tree Shaking**: 自动移除未使用的代码
- **代码分割**: 路由级懒加载，减少初始包体积
- **资源压缩**: Gzip/Brotli 压缩，图片优化
- **缓存策略**: 长期缓存静态资源，合理设置 HTTP 缓存头

### 运行时优化

- **组件缓存**: `<KeepAlive>` 缓存频繁切换的页面
- **虚拟滚动**: 大列表使用虚拟滚动提升渲染性能
- **防抖节流**: 用户输入和网络请求的性能优化
- **内存管理**: 及时清理事件监听器和定时器

### 监控指标

- **Core Web Vitals**: FCP、LCP、CLS 等核心性能指标
- **自定义指标**: 页面切换时间、API 响应时间
- **错误监控**: 自动收集和上报前端错误
- **用户行为**: 通过百度统计分析用户操作模式

## 🛠️ 开发工具

### VSCode 推荐插件

```json
{
  "recommendations": [
    "Vue.volar", // Vue 3 语言支持
    "Vue.vscode-typescript-vue-plugin", // TypeScript Vue 插件
    "esbenp.prettier-vscode", // 代码格式化
    "dbaeumer.vscode-eslint", // ESLint 集成
    "formulahendry.auto-rename-tag", // 自动重命名标签
    "christian-kohler.path-intellisense", // 路径智能提示
    "bradlc.vscode-tailwindcss", // TailwindCSS 支持
    "ms-vscode.vscode-typescript-next" // TypeScript 最新版本
  ]
}
```

### 调试环境

- **Vue DevTools**: 组件状态和事件调试
- **Chrome DevTools**: 网络请求和性能分析
- **桥接调试面板**: `/develop` 页面的 API 调试工具
- **热重载**: Vite 提供毫秒级热更新体验

### 开发规范工具

- **Husky**: Git Hook 管理，提交前自动检查
- **ESLint**: 代码质量检查和自动修复
- **Prettier**: 统一的代码格式化
- **TypeScript**: 编译时类型检查

## 11. 开发规范与指南 (Development Guide)

为确保代码质量、开发效率和长期可维护性，所有团队成员必须遵守以下规范。

### 11.1. 命名规范

- **目录/文件夹**: 全部使用小写 `kebab-case` (短横线连接)。例如: `src/components/business`, `src/composables`。
- **Vue 组件文件**: 使用 `PascalCase` (大驼峰命名) 或文件夹 `index.vue`。例如: `ProductCard.vue`, `BaseModal.vue`。
- **非组件 TS/JS 文件**: 使用 `kebab-case`。例如: `use-responsive.ts`, `api-client.ts`。
- **C# 文件/类/接口**: 使用 `PascalCase`。接口名前加 `I`。例如: `PrinterService.cs`, `IScanner.cs`。
- **变量/函数**: 使用 `camelCase` (小驼峰命名)。例如: `const cartItems = ...`, `function calculateTotal() {}`。
- **CSS 类名**: 使用 `kebab-case`，并可结合 BEM 思想。例如: `.product-card__title`。

### 11.2. Git 工作流与提交规范

- **分支模型**:
  - `main`: 生产分支，只接受来自 `develop` 的合并。
  - `develop`: 开发主分支，集成所有已完成的功能。
  - `feature/xxx`: 功能开发分支，从 `develop` 创建，完成后合并回 `develop`。
  - `fix/xxx`: Bug 修复分支。
  - `refactor/xxx`: 重构分支。

- **提交信息 (Commit Message)**: 遵循约定式提交规范
  - 格式: `<类型>[可选 范围]: <描述>`
  - 示例: `feat(cart): add item removal functionality`
  - 常用类型:
    - `feat`: 新功能
    - `fix`: Bug 修复
    - `docs`: 文档更新
    - `style`: 代码格式调整
    - `refactor`: 代码重构
    - `perf`: 性能优化
    - `test`: 测试相关
    - `chore`: 构建过程或辅助工具的变动
    - `ci`: 持续集成相关
    - `build`: 构建系统相关

### 11.3. 编码规范

- **接口先行**: 所有原生与 Web 的交互，必须先在 `src/bridge/types.ts` 中定义清晰的 TypeScript 接口和事件类型。

- **职责边界**: 严格遵守功能划分。原生只提供"能力"，Web 负责"展现"和"业务编排"。原生接口应是功能性的 (`print`) 而非过程性的 (`openPort`)。

- **Vue 最佳实践**:
  - 优先使用 `<script setup>` 语法。
  - 组合式函数 (`composables`) 应用于封装和复用有状态逻辑。
  - 遵循单向数据流，UI 组件应从 Pinia Store 获取状态，通过 Action 修改状态。
  - 组件 props 定义应尽可能详细，包含 `type`, `required`, `default` 和 `validator`。
  - 遵循 [Vue 官方风格指南](https://vuejs.org/style-guide/)
  - 遵循 [TypeScript 阿里巴巴前端规约](https://alibaba.github.io/f2e-spec/zh/coding/typescript/)

- **C# 最佳实践**:
  - 遵循 SOLID 原则。
  - 使用依赖注入（Dependency Injection）来解耦服务。
  - 异步方法必须使用 `async/await` 并以 `Async` 后缀命名。

- **日志规范**: 关键用户路径、API 调用和错误处理必须记录日志。日志信息需包含充足的上下文（如用户 ID、订单号、错误堆栈）。

### 11.4. 代码审查 (Code Review)

- 所有向 `develop` 和 `main` 分支的合并请求 (Merge Request / Pull Request) 都必须经过至少一位其他团队成员的审查。
- 审查重点: 代码风格、逻辑正确性、可读性、性能、是否符合架构设计。

### 11.5. 组件开发规范

- **组件命名**: 使用 `PascalCase`，业务组件建议加前缀 `Pos`。
- **Props 定义**: 必须包含完整的类型定义和默认值。
- **事件命名**: 使用 `kebab-case`，动词开头。
- **样式规范**: 使用 `scoped` 样式，类名采用 `kebab-case`。

### 11.6. 国际化规范

- **键命名**: 使用点分隔的层级结构，如 `message.error.network`。
- **文本提取**: 所有用户可见文本必须通过 `$t()` 函数进行国际化。
- **默认语言**: 以中文为主要开发语言，英文为辅助语言。

## 📊 项目统计

| 指标                  | 数值    | 说明                       |
| --------------------- | ------- | -------------------------- |
| **代码行数**          | ~5,000+ | 包含 TypeScript、Vue、Less |
| **组件数量**          | 20+     | 基础组件 + 业务组件        |
| **路由页面**          | 10+     | 包含开发、登录、主页面等   |
| **依赖包数量**        | 30+     | 精选高质量依赖包           |
| **构建体积**          | < 500KB | Gzip 压缩后的生产包        |
| **TypeScript 覆盖率** | 95%+    | 严格类型检查               |

## 🗂️ 相关文档

- **📋 [完整架构设计方案](./POS系统前端软件架构设计方案.md)**: 详细的系统架构和设计理念
- **🔧 [桥接层 API 文档](./src/bridge/types/index.ts)**: 完整的 JSBridge 接口定义
- **🎨 [组件库文档](./src/components/)**: 可复用组件的使用说明
- **🌍 [国际化配置](./src/locale/modules/)**: 多语言支持文档

## 📄 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件

## 🤝 贡献指南

我们欢迎任何形式的贡献，包括但不限于：

### 贡献流程

1. **Fork 项目**: 点击右上角 Fork 按钮
2. **创建分支**: `git checkout -b feature/your-feature-name`
3. **提交代码**:
   ```bash
   git add .
   git commit -m "feat(scope): add your feature description"
   ```
4. **推送分支**: `git push origin feature/your-feature-name`
5. **创建 PR**: 在 GitHub 上创建 Pull Request

### 提交规范

遵循约定式提交 (Conventional Commits)：

```bash
# 新功能
git commit -m "feat(bridge): add printer status monitoring"

# Bug 修复
git commit -m "fix(router): resolve navigation guard issue"

# 文档更新
git commit -m "docs: update installation guide"

# 代码重构
git commit -m "refactor(components): optimize button component"
```

### 代码审查

- 所有 PR 必须通过至少一位维护者的审查
- 确保 ESLint 和 TypeScript 检查通过
- 添加适当的测试用例（如适用）
- 更新相关文档

<div align="center">

**🚀 构建现代化的 POS 系统前端解决方案，让开发更加高效和愉悦！**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Vue 3](https://img.shields.io/badge/Vue-3.5+-4FC08D?logo=vue.js&logoColor=white)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8+-3178C6?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![TDesign](https://img.shields.io/badge/TDesign-Latest-0052CC?logo=tencent&logoColor=white)](https://tdesign.tencent.com/vue-next/getting-started)

</div>
