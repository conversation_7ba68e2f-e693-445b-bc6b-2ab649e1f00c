import { createI18n } from 'vue-i18n';
import en from './modules/en';
import zh_cn from './modules/zh-cn';
import { useConfigStore } from '@/store';

// 创建一个返回 i18n 实例的函数在 Pinia 初始化后访问 store
export function createI18nInstance() {
  const configStore = useConfigStore();
  const locale = configStore.locale;
  console.log(locale);
  return createI18n({
    legacy: false,
    locale,
    messages: {
      zh_cn,
      en
    }
  });
}
