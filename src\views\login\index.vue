<template>
  <!-- 左右分栏式布局 -->
  <layout-side-by-side>
    <!-- 左侧内容区域 -->
    <template #left>
      <div class="left-content">
        <!-- 根据历史用户数据动态显示内容 -->
        <!-- 无历史用户时显示Logo -->
        <template v-if="historyUser.length === 0">
          <left-logo />
        </template>
        <!-- 有历史用户时显示用户列表 -->
        <template v-else>
          <div class="history-content">
            <!-- 遍历展示历史登录用户 -->
            <div v-for="item in historyUser" :key="item.id" class="history-user-item"></div>
          </div>
        </template>
        <!-- 应用版本号显示 -->
        <span class="app-version">Ver {{ appVersion }}</span>
        <!-- 客服组件 -->
        <customer-service class="customer-service" />
      </div>
    </template>
    <!-- 右侧内容区域 -->
    <template #right>
      <div class="right-content"></div>
    </template>
  </layout-side-by-side>
</template>

<script setup lang="ts">
/**
 * @file 登录页面
 * @description 负责处理登录页面的逻辑，包括用户状态管理和版本信息展示。
 */
import { computed } from 'vue';

// 布局组件
import { LayoutSideBySide } from '@/layouts/';
// 全局组件
import { CustomerService } from '@/components';
// Pinia 状态管理
import { useUserStore } from '@/store';
// 静态资源
import LeftLogo from '@/assets/logo/left-logo.svg';

// 获取应用版本号
const appVersion = import.meta.env.VITE_APP_VERSION;

// 用户状态
const userStore = useUserStore();
// 历史登录用户
const historyUser = computed(() => userStore.historyUser);

defineOptions({
  name: 'LoginIndex'
});
</script>

<style scoped lang="less">
@import url('./index.less');
</style>
