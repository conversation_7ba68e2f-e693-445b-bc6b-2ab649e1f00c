.top-down-layout {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  padding: var(--layout-spacing-medium);

  .header-panel {
    flex-shrink: 0;
    background-color: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    position: relative;
  }

  .main-panel {
    flex: 1;
    overflow-y: auto;
    background-color: var(--td-bg-color-page);
    position: relative;
  }
}