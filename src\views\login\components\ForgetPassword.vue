<template>
  <div class="forget-password">
    <!-- 标题区域 -->
    <div class="header-section">
      <h1 class="title">忘记密码</h1>
      <p class="subtitle">别担心，我们将协助您重置密码</p>
    </div>

    <!-- 重置表单 -->
    <form class="reset-form" @submit.prevent="handleReset">
      <!-- 手机号输入 -->
      <div class="form-field">
        <label class="field-label">手机号</label>
        <div class="field-input">
          <t-input
            v-model="formData.phone"
            placeholder="请填写管理员手机号"
            size="large"
            :status="errors.phone ? 'error' : 'default'"
          >
            <template #suffix>
              <t-button
                theme="primary"
                variant="text"
                size="small"
                :disabled="!canSendCode || codeSending"
                @click="sendVerificationCode"
              >
                {{ codeButtonText }}
              </t-button>
            </template>
          </t-input>
        </div>
      </div>

      <!-- 验证码输入 -->
      <div class="form-field">
        <label class="field-label">验证码</label>
        <div class="verification-input">
          <pos-verification-code v-model="verificationCode" :length="6" @complete="handleCodeComplete" />
        </div>
      </div>

      <!-- 新密码输入 -->
      <div class="form-field">
        <label class="field-label">新密码</label>
        <div class="field-input">
          <t-input
            v-model="formData.newPassword"
            type="password"
            placeholder="请输入新密码"
            size="large"
            :status="errors.newPassword ? 'error' : 'default'"
          />
        </div>
      </div>

      <!-- 密码要求检查 -->
      <div class="password-requirements">
        <div class="requirement-item">
          <t-checkbox :checked="passwordRequirements.length" :disabled="true"> 必须包含 8 个字符 </t-checkbox>
        </div>
        <div class="requirement-item">
          <t-checkbox :checked="passwordRequirements.complexity" :disabled="true">
            必须至少包含 1个数字、字母
          </t-checkbox>
        </div>
      </div>

      <!-- 重置按钮 -->
      <t-button theme="primary" size="large" block :loading="isLoading" :disabled="!isFormValid" @click="handleReset">
        重置密码
      </t-button>
    </form>

    <!-- 返回登录 -->
    <div class="back-section">
      <button class="back-button" @click="emit('backToLogin')">
        <t-icon name="chevron-left" />
        <span>返回登录</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * @file 忘记密码组件
 * @description 密码重置组件，包含手机验证和密码重置功能
 */
import { reactive, computed, ref } from 'vue';
import { PosVerificationCode } from '@/components';
import { isPhone } from 'frontend-utils';

// 定义事件
const emit = defineEmits<{
  (_e: 'backToLogin'): void;
  (_e: 'resetSuccess'): void;
}>();

// 表单数据
const formData = reactive({
  phone: '',
  newPassword: ''
});

// 验证码
const verificationCode = ref('');

// 表单验证错误
const errors = reactive({
  phone: '',
  newPassword: '',
  code: ''
});

// 加载状态
const isLoading = ref(false);
const codeSending = ref(false);
const countdown = ref(0);

// 密码要求检查
const passwordRequirements = computed(() => ({
  length: formData.newPassword.length >= 8,
  complexity: /^(?=.*[A-Za-z])(?=.*\d)/.test(formData.newPassword)
}));

// 验证码按钮文字
const codeButtonText = computed(() => {
  if (countdown.value > 0) {
    return `${countdown.value}s后重发`;
  }
  return codeSending.value ? '发送中...' : '获取验证码';
});

// 能否发送验证码
const canSendCode = computed(() => {
  return isPhone(formData.phone.trim()) && countdown.value === 0;
});

// 验证码完成处理
const handleCodeComplete = (code: string) => {
  console.log('验证码输入完成:', code);
};

// 表单验证
const isFormValid = computed(() => {
  return (
    formData.phone.trim() !== '' &&
    verificationCode.value.length === 6 &&
    formData.newPassword.trim() !== '' &&
    passwordRequirements.value.length &&
    passwordRequirements.value.complexity
  );
});

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) return;

  codeSending.value = true;
  try {
    // TODO: 实现发送验证码逻辑
    console.log('发送验证码到:', formData.phone);

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 开始倒计时
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error) {
    console.error('发送验证码失败:', error);
  } finally {
    codeSending.value = false;
  }
};

// 重置密码处理
const handleReset = async () => {
  if (!isFormValid.value) return;

  isLoading.value = true;
  try {
    // TODO: 实现密码重置逻辑
    console.log('重置密码:', {
      phone: formData.phone,
      code: verificationCode.value,
      newPassword: formData.newPassword
    });

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 重置成功，触发成功事件
    emit('resetSuccess');
  } catch (error) {
    console.error('重置密码失败:', error);
  } finally {
    isLoading.value = false;
  }
};

defineOptions({
  name: 'ForgetPassword'
});
</script>

<style scoped lang="less">
.forget-password {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  width: 320px;
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.title {
  margin: 0;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 700;
  font-size: 30px;
  line-height: 1.27;
  text-align: center;
  color: var(--text-primary);
}

.subtitle {
  margin: 0;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.375;
  text-align: center;
  color: var(--text-secondary);
}

.reset-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.field-label {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: var(--text-primary);
}

.field-input {
  display: flex;
  width: 100%;
}

.verification-input {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
}

.password-requirements {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-section {
  width: 100%;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  background: transparent;
  padding: 0;
  cursor: pointer;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: var(--text-primary);
  transition: color 0.2s ease;
  margin: auto;

  &:hover {
    color: var(--text-secondary-hover);
  }
}

:deep(.t-input__inner) {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.375;

  &::placeholder {
    color: var(--text-secondary);
  }
}

:deep(.t-checkbox__label) {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: var(--text-primary);
}

:deep(.t-button) {
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
}
</style>
