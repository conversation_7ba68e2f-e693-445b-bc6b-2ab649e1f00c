### 1. 在模板中使用

```vue
<template>
  <!-- 直接使用 -->
  <h1>{{ $t('login.title') }}</h1>
  
  <!-- 作为属性 -->
  <t-input :placeholder="$t('login.form.accountPlaceholder')" />
  
  <!-- 在按钮中使用 -->
  <t-button>{{ $t('component.save') }}</t-button>
</template>
```

### 2. 在脚本中使用

```vue
<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const showMessage = () => {
  // 获取翻译文本
  const title = t('login.title')
  const buttonText = t('component.save')
  
  // 使用翻译文本
  console.log(title, buttonText)
}
</script>
```

### 3. 语言文件结构

#### 中文 (zh-cn.ts)
```typescript
export default {
  component: {
    cancel: '取消',
    action: '确定',
    save: '保存',
    edit: '编辑',
    search: '搜索'
  },
  login: {
    title: '登录到系统',
    form: {
      accountPlaceholder: '请输入账号：admin',
      passwordPlaceholder: '请输入登录密码：admin',
      loginButton: '登录'
    }
  },
  customerService: {
    title: '客服中心',
    description: '如有问题请联系客服',
    phoneNumber: '客服电话：************',
    workTime: '工作时间：9:00-18:00'
  }
}
```

#### 英文 (en.ts)
```typescript
export default {
  component: {
    cancel: 'Cancel',
    action: 'Confirm',
    save: 'Save',
    edit: 'Edit',
    search: 'Search'
  },
  login: {
    title: 'Login to System',
    form: {
      accountPlaceholder: 'Please enter account: admin',
      passwordPlaceholder: 'Please enter password: admin',
      loginButton: 'Login'
    }
  },
  customerService: {
    title: 'Customer Service',
    description: 'Please contact customer service if you have any questions',
    phoneNumber: 'Service Phone: ************',
    workTime: 'Working Hours: 9:00-18:00'
  }
}
```

### 4. 文件位置

```
src/
├── locale/
│   ├── index.ts          # 国际化配置
│   └── modules/
│       ├── zh-cn.ts      # 中文语言包
│       └── en.ts         # 英文语言包
└── store/
    └── modules/
        └── config.ts     # 语言配置状态管理
```
