<template>
  <t-config-provider :global-config="currentLocaleConfig">
    <alive-router-view />
  </t-config-provider>
  <!-- 开发用-->
  <template v-if="isDevelopment">
    <t-sticky-tool :style="{ position: 'fixed', overflow: 'hidden' }" :offset="[-60, 20]" @click="handleClick">
      <t-sticky-item label="主题" @click="toggleDark()">
        <template #icon>
          <icon-font name="mode-dark" size="22px" />
        </template>
      </t-sticky-item>
      <t-sticky-item label="组件" @click="$router.push('/develop')">
        <template #icon>
          <icon-font name="code" size="22px" />
        </template>
      </t-sticky-item>
    </t-sticky-tool>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useDark, useToggle } from '@vueuse/core';
import { useConfigStore } from '@/store';
import { AliveRouterView } from '@/components';
import { IconFont } from 'tdesign-icons-vue-next';
import { useRouter } from 'vue-router';
import zhLocale from 'tdesign-vue-next/es/locale/zh_CN';
import enLocale from 'tdesign-vue-next/es/locale/en_US';

const isDevelopment = import.meta.env.VITE_NODE_ENV === 'development';

// 使用 Pinia 的 useConfigStore hook 获取全局配置状态
const configStore = useConfigStore();

// 计算属性，根据 store 中的 locale 状态动态返回 TDesign 的国际化语言包
const currentLocaleConfig = computed(() => {
  return configStore.locale === 'zh_cn' ? zhLocale : enLocale;
});

// 使用 VueUse 的 useDark 来管理暗黑模式
// 配置为使用 theme-mode 属性，与现有的 CSS 主题变量匹配
const isDark = useDark({
  selector: 'html',
  attribute: 'theme-mode',
  valueDark: 'dark',
  valueLight: 'light'
});

// 创建切换函数
const toggleDark = useToggle(isDark);
const router = useRouter();
const handleClick = (context: any) => {
  if (context.item.label === '主题') {
    toggleDark();
  } else if (context.item.label === '组件') {
    router.push('/develop');
  }
};
</script>
