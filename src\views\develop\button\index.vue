<template>
  <div class="doc-demo">
    <div class="doc-card">
      <h3>Button 按钮组件使用示例</h3>

      <!-- 基础按钮 -->
      <div class="doc-section">
        <h4>基础按钮：</h4>
        <div class="doc-item">
          <span class="label">基础用法：</span>
          <t-button>默认按钮</t-button>
          <t-button theme="primary">主要按钮</t-button>
          <t-button theme="success">成功按钮</t-button>
          <t-button theme="warning">警告按钮</t-button>
          <t-button theme="danger">危险按钮</t-button>
        </div>
      </div>

      <!-- 按钮尺寸 -->
      <div class="doc-section">
        <h4>按钮尺寸：</h4>
        <div class="doc-item">
          <span class="label">不同尺寸：</span>
          <t-button size="small">小按钮</t-button>
          <t-button size="medium">中按钮</t-button>
          <t-button size="large">大按钮</t-button>
        </div>
      </div>

      <!-- 按钮状态 -->
      <div class="doc-section">
        <h4>按钮状态：</h4>
        <div class="doc-item">
          <span class="label">禁用状态：</span>
          <t-button disabled>禁用按钮</t-button>
          <t-button theme="primary" disabled>主要禁用</t-button>
          <t-button loading>加载中</t-button>
          <t-button theme="primary" :loading="isLoading" @click="handleLoadingDemo">
            {{ isLoading ? '加载中...' : '点击加载' }}
          </t-button>
        </div>
      </div>

      <!-- 图标按钮 -->
      <div class="doc-section">
        <h4>图标按钮：</h4>
        <div class="doc-item">
          <span class="label">带图标：</span>
          <t-button>
            <template #icon>
              <t-icon name="add" />
            </template>
            添加
          </t-button>
          <t-button theme="primary">
            <template #icon>
              <t-icon name="search" />
            </template>
            搜索
          </t-button>
          <t-button shape="circle">
            <t-icon name="setting" />
          </t-button>
          <t-button shape="round">
            <template #icon>
              <t-icon name="download" />
            </template>
            下载
          </t-button>
        </div>
      </div>

      <!-- 按钮组 -->
      <div class="doc-section">
        <h4>按钮组：</h4>
        <div class="doc-item">
          <span class="label">组合使用：</span>
          <t-button-group>
            <t-button>左侧</t-button>
            <t-button>中间</t-button>
            <t-button>右侧</t-button>
          </t-button-group>
        </div>
      </div>

      <!-- 文字按钮 -->
      <div class="doc-section">
        <h4>文字按钮：</h4>
        <div class="doc-item">
          <span class="label">链接样式：</span>
          <t-button variant="text">文字按钮</t-button>
          <t-button variant="text" theme="primary">主要文字</t-button>
          <t-button variant="text" theme="danger">危险文字</t-button>
        </div>
      </div>

      <!-- 块级按钮 -->
      <div class="doc-section">
        <h4>块级按钮：</h4>
        <div class="doc-item">
          <t-button block theme="primary">块级按钮（占满容器宽度）</t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineOptions({
  name: 'ButtonDemo'
});

// 加载状态演示
const isLoading = ref(false);

// 加载演示处理
const handleLoadingDemo = () => {
  isLoading.value = true;
  setTimeout(() => {
    isLoading.value = false;
  }, 2000);
};
</script>

<style lang="less" scoped>
@import '@/style/example.less';
</style>
