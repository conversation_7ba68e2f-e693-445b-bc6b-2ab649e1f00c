:root[theme-mode='light'] {
  --td-brand-color-1: #f2f4ff;
  --td-brand-color-2: #dbe3ff;
  --td-brand-color-3: #b8c9ff;
  --td-brand-color-4: #8fadff;
  --td-brand-color-5: #6690ff;
  --td-brand-color-6: #3d73ff;
  --td-brand-color-7: #0055ff;
  --td-brand-color-8: #0044cc;
  --td-brand-color-9: #003399;
  --td-brand-color-10: #002266;

  --td-warning-color-1: #fff1e9;
  --td-warning-color-2: #ffd9c2;
  --td-warning-color-3: #ffb98c;
  --td-warning-color-4: #fa9550;
  --td-warning-color-5: #e37318;
  --td-warning-color-6: #be5a00;
  --td-warning-color-7: #954500;
  --td-warning-color-8: #713300;
  --td-warning-color-9: #532300;
  --td-warning-color-10: #3b1700;

  --td-error-color-1: #fff0ed;
  --td-error-color-2: #ffd8d2;
  --td-error-color-3: #ffb9b0;
  --td-error-color-4: #ff9285;
  --td-error-color-5: #f6685d;
  --td-error-color-6: #d54941;
  --td-error-color-7: #ad352f;
  --td-error-color-8: #881f1c;
  --td-error-color-9: #68070a;
  --td-error-color-10: #490002;

  --td-success-color-1: #e3f9e9;
  --td-success-color-2: #c6f3d7;
  --td-success-color-3: #92dab2;
  --td-success-color-4: #56c08d;
  --td-success-color-5: #2ba471;
  --td-success-color-6: #008858;
  --td-success-color-7: #006c45;
  --td-success-color-8: #005334;
  --td-success-color-9: #003b23;
  --td-success-color-10: #002515;

  --td-gray-color-1: #f3f3f3;
  --td-gray-color-2: #eee;
  --td-gray-color-3: #e7e7e7;
  --td-gray-color-4: #dcdcdc;
  --td-gray-color-5: #c5c5c5;
  --td-gray-color-6: #a6a6a6;
  --td-gray-color-7: #8b8b8b;
  --td-gray-color-8: #777;
  --td-gray-color-9: #5e5e5e;
  --td-gray-color-10: #4b4b4b;
  --td-gray-color-11: #383838;
  --td-gray-color-12: #2c2c2c;
  --td-gray-color-13: #242424;
  --td-gray-color-14: #181818;

  // 文字 & 图标 颜色
  --td-font-white-1: rgba(255, 255, 255, 100%);
  --td-font-white-2: rgba(255, 255, 255, 55%);
  --td-font-white-3: rgba(255, 255, 255, 35%);
  --td-font-white-4: rgba(255, 255, 255, 22%);
  --td-font-gray-1: rgba(0, 0, 0, 90%);
  --td-font-gray-2: rgba(0, 0, 0, 60%);
  --td-font-gray-3: rgba(0, 0, 0, 40%);
  --td-font-gray-4: rgba(0, 0, 0, 26%);

  // 基础颜色
  --td-brand-color: var(--td-brand-color-7); // 色彩-品牌-可操作
  --td-warning-color: var(--td-warning-color-5); // 色彩-功能-警告
  --td-error-color: var(--td-error-color-6); // 色彩-功能-失败
  --td-success-color: var(--td-success-color-5); // 色彩-功能-成功

  // 基础颜色的扩展 用于 hover / 聚焦 / 禁用 / 点击 等状态
  --td-brand-color-hover: var(--td-brand-color-6); // hover态
  --td-brand-color-focus: var(--td-brand-color-2); // focus态，包括鼠标和键盘
  --td-brand-color-active: var(--td-brand-color-8); // 点击态
  --td-brand-color-disabled: var(--td-brand-color-3); // 禁用态
  --td-brand-color-light: var(--td-brand-color-1); // 浅色的选中态

  // 警告色扩展
  --td-warning-color-hover: var(--td-warning-color-4);
  --td-warning-color-focus: var(--td-warning-color-2);
  --td-warning-color-active: var(--td-warning-color-6);
  --td-warning-color-disabled: var(--td-warning-color-3);
  --td-warning-color-light: var(--td-warning-color-1);

  // 失败/错误色扩展
  --td-error-color-hover: var(--td-error-color-5);
  --td-error-color-focus: var(--td-error-color-2);
  --td-error-color-active: var(--td-error-color-7);
  --td-error-color-disabled: var(--td-error-color-3);
  --td-error-color-light: var(--td-error-color-1);

  // 成功色扩展
  --td-success-color-hover: var(--td-success-color-4);
  --td-success-color-focus: var(--td-success-color-2);
  --td-success-color-active: var(--td-success-color-6);
  --td-success-color-disabled: var(--td-success-color-3);
  --td-success-color-light: var(--td-success-color-1);

  // 遮罩
  --td-mask-active: rgba(0, 0, 0, 60%); // 遮罩-弹出
  --td-mask-disabled: rgba(255, 255, 255, 60%); // 遮罩-禁用

  // 背景色
  --td-bg-color-page: var(--td-gray-color-2); // 色彩 - page
  --td-bg-color-container: #fff; // 色彩 - 容器
  --td-bg-color-container-hover: var(--td-gray-color-1); // 色彩 - 容器 - hover
  --td-bg-color-container-active: var(--td-gray-color-3); // 色彩 - 容器 - active
  --td-bg-color-container-select: #fff; // 色彩 - 容器 - select
  --td-bg-color-secondarycontainer: var(--td-gray-color-1); // 色彩 - 次级容器
  --td-bg-color-secondarycontainer-hover: var(--td-gray-color-2); // 色彩 - 次级容器 - hover
  --td-bg-color-secondarycontainer-active: var(--td-gray-color-4); // 色彩 - 次级容器 - active
  --td-bg-color-component: var(--td-gray-color-3); // 色彩 - 组件
  --td-bg-color-component-hover: var(--td-gray-color-4); // 色彩 - 组件 - hover
  --td-bg-color-component-active: var(--td-gray-color-6); // 色彩 - 组件 - active
  --td-bg-color-secondarycomponent: var(--td-gray-color-4); // 色彩 - 次级组件
  --td-bg-color-secondarycomponent-hover: var(--td-gray-color-5); // 色彩 - 次级组件 - hover
  --td-bg-color-secondarycomponent-active: var(--td-gray-color-6); // 色彩 - 次级组件 - active
  --td-bg-color-component-disabled: var(--td-gray-color-2); // 色彩 - 组件 - disabled

  // 特殊组件背景色，目前只用于 button、input 组件多主题场景，浅色主题下固定为白色，深色主题下为 transparent 适配背景颜色
  --td-bg-color-specialcomponent: #fff;

  // 文本颜色
  --td-text-color-primary: var(--td-font-gray-1); // 色彩-文字-主要
  --td-text-color-secondary: var(--td-font-gray-2); // 色彩-文字-次要
  --td-text-color-placeholder: var(--td-font-gray-3); // 色彩-文字-占位符/说明
  --td-text-color-disabled: var(--td-font-gray-4); // 色彩-文字-禁用
  --td-text-color-anti: #fff; // 色彩-文字-反色
  --td-text-color-brand: var(--td-brand-color-8); // 色彩-文字-品牌
  --td-text-color-link: var(--td-brand-color-8); // 色彩-文字-链接

  // 分割线
  --td-border-level-1-color: var(--td-gray-color-3);
  --td-component-stroke: var(--td-gray-color-3);
  // 边框
  --td-border-level-2-color: var(--td-gray-color-4);
  --td-component-border: var(--td-gray-color-4);
}