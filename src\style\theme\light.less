:root[theme-mode="light"] {
  --bg-primary: var(--basic-white);
  --bg-primary_alt: var(--basic-white);
  --bg-primary_hover: var(--gray-light-50);
  --bg-primary_solid: var(--gray-light-950);
  --bg-secondary: var(--gray-light-50);
  --bg-secondary_alt: var(--gray-light-50);
  --bg-secondary_hover: var(--gray-light-100);
  --bg-secondary_subtle: var(--gray-light-25);
  --bg-secondary-solid: var(--gray-light-600);
  --bg-tertiary: var(--gray-light-100);
  --bg-quaternary: var(--gray-light-200);
  --bg-active: var(--gray-light-50);
  --bg-disabled: var(--gray-light-200);
  --bg-disabled_subtle: var(--gray-light-100);
  --bg-overlay: var(--basic-overlay-light-mode);
  --bg-brand-primary: var(--brand-50);
  --bg-brand-primary_alt: var(--brand-50);
  --bg-brand-secondary: var(--brand-100);
  --bg-brand-solid: var(--brand-600);
  --bg-brand-solid_hover: var(--brand-700);
  --bg-brand-section: var(--brand-800);
  --bg-brand-section_subtle: var(--brand-700);
  --bg-error-primary: var(--error-50);
  --bg-error-secondary: var(--error-100);
  --bg-error-solid: var(--error-600);
  --bg-error-solid_hover: var(--error-700);
  --bg-warning-primary: var(--warning-50);
  --bg-warning-secondary: var(--warning-100);
  --bg-warning-solid: var(--warning-600);
  --bg-success-primary: var(--success-50);
  --bg-success-secondary: var(--success-100);
  --bg-success-solid: var(--success-600);
  --fg-primary: var(--gray-light-900);
  --fg-secondary: var(--gray-light-700);
  --fg-secondary_hover: var(--gray-light-800);
  --fg-tertiary: var(--gray-light-600);
  --fg-tertiary_hover: var(--gray-light-700);
  --fg-quaternary: var(--gray-light-400);
  --fg-quaternary_hover: var(--gray-light-500);
  --fg-white: var(--basic-white);
  --fg-disabled: var(--gray-light-400);
  --fg-disabled_subtle: var(--gray-light-300);
  --fg-brand-primary: var(--brand-600);
  --fg-brand-primary_alt: var(--brand-600);
  --fg-brand-secondary: var(--brand-500);
  --fg-brand-secondary_alt: var(--brand-500);
  --fg-error-primary: var(--error-600);
  --fg-error-secondary: var(--error-500);
  --fg-warning-primary: var(--warning-600);
  --fg-warning-secondary: var(--warning-500);
  --fg-success-primary: var(--success-600);
  --fg-success-secondary: var(--success-500);
  --border-primary: var(--gray-light-300);
  --border-secondary: var(--gray-light-200);
  --border-secondary_alt: #00000014;
  --border-tertiary: var(--gray-light-100);
  --border-disabled: var(--gray-light-300);
  --border-disabled_subtle: var(--gray-light-200);
  --border-brand: var(--brand-500);
  --border-error: var(--error-500);
  --border-error_subtle: var(--error-300);
  --text-primary: var(--gray-light-900);
  --text-primary_on-brand: var(--basic-white);
  --text-secondary: var(--gray-light-700);
  --text-secondary-hover: var(--gray-light-800);
  --text-secondary_on-brand: var(--brand-200);
  --text-tertiary: var(--gray-light-500);
  --text-tertiary_hover: var(--gray-light-700);
  --text-tertiary_on-brand: var(--brand-300);
  --text-quaternary: var(--gray-light-500);
  --text-quaternary_on-brand: var(--brand-300);
  --text-white: var(--basic-white);
  --text-disabled: var(--gray-light-500);
  --text-placeholder: var(--gray-light-300);
  --text-brand-primary: var(--brand-900);
  --text-brand-secondary: var(--brand-700);
  --text-brand-secondary-hover: var(--brand-800);
  --text-brand-tertiary: var(--brand-600);
  --text-error-primary: var(--error-600);
  --text-error-primary_hover: var(--error-700);
  --text-warning-primary: var(--warning-600);
  --text-success-primary: var(--success-600);
  --tag-gray-50: var(--gray-light-50);
  --tag-gray-100: var(--gray-light-100);
  --tag-gray-200: var(--gray-light-200);
  --tag-gray-300: var(--gray-light-300);
  --tag-gray-400: var(--gray-light-400);
  --tag-gray-500: var(--gray-light-500);
  --tag-gray-600: var(--gray-light-600);
  --tag-gray-700: var(--gray-light-700);
  --tag-gray-800: var(--gray-light-800);
  --tag-gray-900: var(--gray-light-900);
  --tag-brand-50: var(--brand-50);
  --tag-brand-100: var(--brand-100);
  --tag-brand-200: var(--brand-200);
  --tag-brand-300: var(--brand-300);
  --tag-brand-400: var(--brand-400);
  --tag-brand-500: var(--brand-500);
  --tag-brand-600: var(--brand-600);
  --tag-brand-700: var(--brand-700);
  --tag-brand-800: var(--brand-800);
  --tag-brand-900: var(--brand-900);
  --tag-error-50: var(--error-50);
  --tag-error-100: var(--error-100);
  --tag-error-200: var(--error-200);
  --tag-error-300: var(--error-300);
  --tag-error-400: var(--error-400);
  --tag-error-500: var(--error-500);
  --tag-error-600: var(--error-600);
  --tag-error-700: var(--error-700);
  --tag-warning-50: var(--warning-50);
  --tag-warning-100: var(--warning-100);
  --tag-warning-200: var(--warning-200);
  --tag-warning-300: var(--warning-300);
  --tag-warning-400: var(--warning-400);
  --tag-warning-500: var(--warning-500);
  --tag-warning-600: var(--warning-600);
  --tag-warning-700: var(--warning-700);
  --tag-success-50: var(--success-50);
  --tag-success-100: var(--success-100);
  --tag-success-200: var(--success-200);
  --tag-success-300: var(--success-300);
  --tag-success-400: var(--success-400);
  --tag-success-500: var(--success-500);
  --tag-success-600: var(--success-600);
  --tag-success-700: var(--success-700);
  --tag-purple-50: var(--purple-50);
  --tag-purple-100: var(--purple-100);
  --tag-purple-200: var(--purple-200);
  --tag-purple-300: var(--purple-300);
  --tag-purple-400: var(--purple-400);
  --tag-purple-500: var(--purple-500);
  --tag-purple-600: var(--purple-600);
  --tag-purple-700: var(--purple-700);
  --tag-blue-50: var(--blue-light-50);
  --tag-blue-100: var(--blue-light-100);
  --tag-blue-200: var(--blue-light-200);
  --tag-blue-300: var(--blue-light-300);
  --tag-blue-400: var(--blue-light-400);
  --tag-blue-500: var(--blue-light-500);
  --tag-blue-600: var(--blue-light-600);
  --tag-blue-700: var(--blue-light-700);
  --tag-indigo-50: var(--indigo-50);
  --tag-indigo-100: var(--indigo-100);
  --tag-indigo-200: var(--indigo-200);
  --tag-indigo-300: var(--indigo-300);
  --tag-indigo-400: var(--indigo-400);
  --tag-indigo-500: var(--indigo-500);
  --tag-indigo-600: var(--indigo-600);
  --tag-indigo-700: var(--indigo-700);
  --tag-orange-50: var(--orange-50);
  --tag-orange-100: var(--orange-100);
  --tag-orange-200: var(--orange-200);
  --tag-orange-300: var(--orange-300);
  --tag-orange-400: var(--orange-400);
  --tag-orange-500: var(--orange-500);
  --tag-orange-600: var(--orange-600);
  --tag-orange-700: var(--orange-700);
  --shadow-1-8: #10182814;
  --shadow-1-3: #10182814;
  --shadow-1-offsetx-1: 0px;
  --shadow-1-offsety-1: 12px;
  --shadow-1-blur-1: 16px;
  --shadow-1-spread-1: -4px;
  --shadow-1-offsetx-2: 0px;
  --shadow-1-offsety-2: 4px;
  --shadow-1-blur-2: 6px;
  --shadow-1-spread-2: -2px;
}