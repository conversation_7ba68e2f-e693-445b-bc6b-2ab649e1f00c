import { request } from '@/utils/request';
import type { LoginRequest, LoginResponse, PhoneLoginRequest } from './types/login';

// 账号密码登录
export function postPasswordLogin(data: LoginRequest, options = {}) {
  return request<LoginResponse>({
    url: `/login/password`,
    method: 'POST',
    data,
    ...options
  });
}

export function postPhoneLogin(data: PhoneLoginRequest, options = {}) {
  return request<LoginResponse>({
    url: `/login/phone`,
    method: 'POST',
    data,
    ...options
  });
}
