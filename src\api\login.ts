import { request } from '@/utils/request';
import type { LoginRequest, LoginResponse, PhoneLoginRequest } from './types/login';

const url = import.meta.env.VITE_API_AUTH_URL;

// 账号密码登录
export function postPasswordLogin(data: LoginRequest, options = {}) {
  return request<LoginResponse>({
    url: `${url}/login/password`,
    method: 'POST',
    data,
    ...options
  });
}

export function postPhoneLogin(data: PhoneLoginRequest, options = {}) {
  return request<LoginResponse>({
    url: `${url}/login/phone`,
    method: 'POST',
    data,
    ...options
  });
}
