import axios, { InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { getUrlParam } from 'frontend-utils';
import { useUserStore } from '@/store';
import { useI18n } from 'vue-i18n';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';

// 类型定义
interface DownloadParams {
  url: string;
  name?: string;
  data?: Record<string, any>;
}

interface AppAxiosRequestConfig extends InternalAxiosRequestConfig {
  ignoreToken?: boolean;
}

interface AppError extends Error {
  msg?: string;
  response?: {
    status: number;
    config: {
      url: string;
    };
  };
}

export const url = (path: string): string => {
  return import.meta.env.VITE_API_URL + path;
};

export const download = async ({ url, name, data }: DownloadParams): Promise<void> => {
  const response = (await request({
    method: 'get',
    url: url,
    data,
    responseType: 'blob'
  })) as Blob;

  if ((response as any).type === 'application/json') {
    const reader = new FileReader();
    reader.addEventListener('loadend', () => {
      const result = JSON.parse(reader.result as string);

      // 此处增加自定义错误处理
      console.log(result);
    });
    reader.readAsText(response);
    return;
  }

  const urlObject = window.URL.createObjectURL(response);

  const downloadLink = document.createElement('a');
  downloadLink.href = urlObject;
  downloadLink.download = name || '';
  downloadLink.target = '_blank';
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};

export const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: import.meta.env.VITE_API_TIMEOUT // 超时
});

// 发起请求之前的拦截器
request.interceptors.request.use(
  (config: AppAxiosRequestConfig) => {
    if (!config.ignoreToken) {
      const configStore = useUserStore();
      const token = configStore.token;
      config.headers.Authorization = 'Bearer ' + token;
    }

    // get 请求转换参数处理
    if (config.method && config.data && config.method.toLowerCase() === 'get') {
      const getParam: Record<string, string> = {};
      for (const o in config.data) {
        getParam[o] = config?.data[o] === undefined ? '' : encodeURIComponent(config?.data[o]);
      }
      config.url += '?' + getUrlParam(getParam);
    }

    if (config.url && config.url.startsWith('http')) {
      config.baseURL = '';
    }

    return config;
  },
  (error: AxiosError) => Promise.reject(new Error(error.message))
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    // 处理二进制流
    if (response.config.responseType === 'blob') {
      return response.data;
    }

    const { data } = response;

    // 根据您项目的业务逻辑进行调整
    // 此处假设 code/status 为 200 是成功状态，您可能需要根据实际情况修改
    if (data.code !== 200 || data.status !== 200) {
      MessagePlugin.warning(data.msg || '响应数据格式错误');
    }

    return data;
  },
  (error: AxiosError & AppError) => {
    console.log(error);
    const status = error.response?.status;

    const { t } = useI18n();

    switch (status) {
      case 400:
        error.msg = t('message.error400');
        break;
      case 401:
        error.msg = t('message.error401');
        const userStore = useUserStore();
        const router = useRouter();
        userStore.setToken('');
        router.push({
          name: 'LoginIndex'
        });
        break;
      case 403:
        error.msg = t('message.error403');
        break;
      case 404:
        error.msg = t('message.error404');
        break;
      case 408:
        error.msg = t('message.error408');
        break;
      case 500:
        error.msg = t('message.error500');
        break;
      case 501:
        error.msg = t('message.error501');
        break;
      case 502:
        error.msg = t('message.error502');
        break;
      case 503:
        error.msg = t('message.error503');
        break;
      case 504:
        error.msg = t('message.error504');
        break;
      case 505:
        error.msg = t('message.error505');
        break;
      default:
        error.msg = t('message.error500');
        break;
    }

    MessagePlugin.error(error.msg);

    return Promise.reject(new Error(error.message));
  }
);
