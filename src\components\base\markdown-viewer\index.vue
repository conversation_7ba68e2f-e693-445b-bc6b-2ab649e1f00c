<template>
  <div class="markdown-viewer">
    <!-- Markdown 内容插槽 -->
    <slot name="markdown" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, nextTick } from 'vue';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';

/**
 * Markdown 查看器组件
 * 用于显示 markdown 内容并提供代码高亮功能
 */

/**
 * 组件挂载后执行代码高亮
 */
onMounted(async () => {
  // 等待 DOM 完全渲染
  await nextTick();

  // 短暂延迟确保内容已插入
  setTimeout(() => {
    // 获取所有代码块并根据类型应用不同的语法高亮
    const codeBlocks = document.querySelectorAll('.markdown-viewer pre code');

    codeBlocks.forEach(block => {
      // 获取原始语言类名
      const originalClasses = block.className;
      const isVue = originalClasses.includes('language-vue') || originalClasses.includes('vue');

      // 移除现有的语言类名
      block.className = block.className.replace(/language-\w+/g, '');

      if (isVue) {
        // Vue 代码块使用 XML 语法高亮
        block.classList.add('language-xml');
      } else {
        // 其他代码块使用 JavaScript 语法高亮
        block.classList.add('language-javascript');
      }

      // 使用 highlight.js 进行语法高亮
      hljs.highlightElement(block as HTMLElement);
    });
  }, 100);
});
</script>

<style lang="less" scoped>
.markdown-viewer {
  :deep(.hljs) {
    background-color: transparent;
  }

  :deep(pre) {
    background-color: #212121;
    border-radius: 4px;
    padding: 10px;

    code {
      font-family:
        ui-monospace,
        SFMono-Regular,
        Menlo,
        Monaco,
        Consolas,
        Liberation Mono,
        Courier New,
        monospace;
    }
  }
}
</style>
