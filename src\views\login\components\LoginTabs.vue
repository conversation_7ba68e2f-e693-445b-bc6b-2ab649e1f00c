<template>
  <div class="login-tabs">
    <!-- 欢迎标题区域 -->
    <div class="welcome-section">
      <h1 class="welcome-title">欢迎使用 掌柜智囊</h1>
      <p class="welcome-subtitle">请选择您的登录方式</p>
    </div>

    <!-- 标签页切换 -->
    <div class="tabs-section">
      <!-- 标签页头部 -->
      <div class="tabs-header">
        <button class="tab-button" :class="{ active: activeTab === 'qr' }" @click="activeTab = 'qr'">
          <t-icon v-if="activeTab === 'qr'" name="help" />
          扫码登录
        </button>
        <button class="tab-button" :class="{ active: activeTab === 'account' }" @click="activeTab = 'account'">
          账号登录
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="tabs-content">
        <!-- 扫码登录内容 -->
        <QrLogin v-if="activeTab === 'qr'" />
        <!-- 账号登录内容 -->
        <AccountLogin v-else-if="activeTab === 'account'" @show-forget-password="emit('showForgetPassword')" />
      </div>
    </div>

    <!-- 底部注册链接（仅在账号登录时显示） -->
    <div v-if="activeTab === 'account'" class="register-link">
      <button class="text-button">
        <span class="text-secondary">还没有账号？</span>
        <span class="text-brand">立即注册</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * @file 登录标签页组件
 * @description 登录页面的主要标签页切换组件，包含扫码登录和账号登录两种方式
 */
import { ref } from 'vue';
import QrLogin from './QrLogin.vue';
import AccountLogin from './AccountLogin.vue';

// 定义事件
const emit = defineEmits<{ (_e: 'showForgetPassword'): void }>();

// 当前激活的标签页
const activeTab = ref<'qr' | 'account'>('qr');

defineOptions({
  name: 'LoginTabs'
});
</script>

<style scoped lang="less">
.login-tabs {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  width: 320px;
}

.welcome-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.welcome-title {
  margin: 0;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 700;
  font-size: 30px;
  line-height: 1.27;
  text-align: center;
  color: var(--text-primary);
}

.welcome-subtitle {
  margin: 0;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.375;
  text-align: center;
  color: var(--text-secondary);
}

.tabs-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  width: 100%;
}

.tabs-header {
  display: flex;
  width: 100%;
  gap: 4px;
  padding: 4px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-secondary);
  border-radius: 10px;
}

.tab-button {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--bg-secondary_hover);
  }

  &.active {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    box-shadow:
      0px 1px 3px 0px rgba(10, 13, 18, 0.1),
      0px 1px 2px 0px rgba(10, 13, 18, 0.1);
  }
}

.tabs-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.register-link {
  width: 100%;
  justify-content: center;
  display: flex;
}

.text-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  background: transparent;
  padding: 0;
  cursor: pointer;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;

  .text-secondary {
    color: var(--text-secondary);
  }

  .text-brand {
    color: var(--text-brand-tertiary);
  }

  &:hover {
    .text-brand {
      color: var(--text-brand-secondary-hover);
    }
  }
}
</style>
