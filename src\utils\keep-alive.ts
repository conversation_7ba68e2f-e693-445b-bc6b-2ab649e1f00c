import { useConfigStore } from '../store';
import { cloneDeep } from 'frontend-utils';
import type { RouteLocationNormalized } from 'vue-router';

export const keepAliveChange = (
  keepAliveConfig: Record<string, string[]>,
  to: RouteLocationNormalized,
  from: RouteLocationNormalized
) => {
  const configStore = useConfigStore();
  // 动态控制 keep-alive include 包含组件 ( 此处 组件为组件的真实 name, 非 router name, 请保持组件名称与路由名称一致 )
  const keepAliveList = cloneDeep(configStore.keepAliveList);

  // 根据来源查找到达列表
  const fromConfig = keepAliveConfig[from.name as string];

  if (fromConfig) {
    // 检查目的路由是否于列表, 设置缓存
    if (fromConfig.includes(to.name as string) || fromConfig.includes('*')) {
      // 不包含当前, 则追加
      if (!keepAliveList.includes(from.name as string)) {
        keepAliveList.push(from.name as string);
      }
    } else {
      // 包含则移除
      if (keepAliveList.includes(from.name as string)) {
        keepAliveList.splice(keepAliveList.indexOf(from.name as string), 1);
      }
    }
  }

  // 根据到达查找来源列表
  const toConfig = keepAliveConfig[to.name as string];

  // 检查来源路由是否于列表, 不包含则检查缓存移除
  if (toConfig && !toConfig.includes(from.name as string) && !toConfig.includes('*')) {
    // 包含则移除
    if (keepAliveList.includes(to.name as string)) {
      keepAliveList.splice(keepAliveList.indexOf(to.name as string), 1);
    }
  }
  configStore.setKeepAliveList(keepAliveList);
};
