import type { PropType } from 'vue';

export const avatarProps = {
  /**
   * The URL for the avatar image.
   */
  image: {
    type: String,
    default: ''
  },
  /**
   * The text to display if no image is provided.
   */
  text: {
    type: String,
    default: ''
  },
  /**
   * The size of the avatar.
   * @values 'xs', 'sm', 'md', 'lg', 'xl', '2xl'
   */
  size: {
    type: String as PropType<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>,
    default: 'md'
  },
  /**
   * The status icon to display.
   * @values 'online', 'level'
   */
  statusIcon: {
    type: String as PropType<'online' | 'level'>,
    default: null
  },
  /**
   * Whether the avatar is in a focused state.
   */
  isFocused: {
    type: Boolean,
    default: false
  }
};
