import type { ExtractPropTypes, PropType } from 'vue';

export const verificationCodeProps = {
  /**
   * 验证码长度
   * @default 6
   */
  length: {
    type: Number,
    default: 6
  },
  /**
   * 验证码值
   */
  modelValue: {
    type: String,
    default: ''
  },
  /**
   * 是否禁用
   */
  disabled: {
    type: Boolean,
    default: false
  },
  /**
   * 是否只读
   */
  readonly: {
    type: Boolean,
    default: false
  },
  /**
   * 是否显示分隔符
   * @default true
   */
  showSeparator: {
    type: Boolean,
    default: true
  },
  /**
   * 分隔符文本
   * @default '-'
   */
  separatorText: {
    type: String,
    default: '-'
  },
  /**
   * 分隔符位置（从0开始的索引数组）
   * @default [2] 表示在第3个位置后插入分隔符
   */
  separatorPositions: {
    type: Array as PropType<number[]>,
    default: () => [2]
  }
};

export type VerificationCodeProps = ExtractPropTypes<typeof verificationCodeProps>;

export interface VerificationCodeEmits {
  /** 验证码变化 */
  'update:modelValue': [value: string];
  /** 验证码输入完成 */
  complete: [value: string];
  /** 验证码变化 */
  change: [value: string];
}
