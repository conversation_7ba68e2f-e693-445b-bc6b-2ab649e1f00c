import type { PropType } from 'vue';

export type Theme = 'success' | 'warning';

export const props = {
  /**
   * Controls the visibility of the dialog.
   * @type {boolean}
   * @default false
   */
  visible: {
    type: Boolean,
    default: false
  },
  /**
   * The theme of the alert dialog.
   * @type {'success' | 'warning'}
   * @default 'warning'
   */
  theme: {
    type: String as PropType<Theme>,
    default: 'warning'
  },
  /**
   * The title of the alert dialog.
   * @type {string}
   * @default ''
   */
  title: {
    type: String,
    default: ''
  },
  /**
   * The content of the alert dialog.
   * @type {string}
   * @default ''
   */
  content: {
    type: String,
    default: ''
  },
  /**
   * The text for the confirm button.
   * @type {string}
   * @default 'Confirm'
   */
  confirmBtn: {
    type: String,
    default: 'Confirm'
  },
  /**
   * The text for the cancel button.
   * @type {string}
   * @default 'Cancel'
   */
  cancelBtn: {
    type: String,
    default: 'Cancel'
  },
  /**
   * The icon for the alert dialog.
   * @type {string}
   * @default ''
   */
  icon: {
    type: String,
    default: ''
  }
};
