import { ref, computed, watch, nextTick } from 'vue';
import type { VerificationCodeEmits, VerificationCodeProps } from '@/components/bussiness/pos-verification-code/props';

export function useVerificationCode(props: VerificationCodeProps, emit: VerificationCodeEmits) {
  // 验证码数组
  const codeArray = ref<string[]>(Array(props.length).fill(''));

  // 验证码输入框引用
  const codeInputRefs = ref<any[]>([]);

  // 设置验证码输入框引用
  const setCodeInputRef = (el: any, index: number) => {
    if (el) {
      codeInputRefs.value[index] = el;
    }
  };

  // 完整验证码
  const fullCode = computed(() => {
    return codeArray.value.join('');
  });

  // 监听外部传入的值变化
  watch(
    () => props.modelValue,
    newValue => {
      if (newValue !== fullCode.value) {
        const chars = newValue.split('');
        codeArray.value = Array(props.length)
          .fill('')
          .map((_, index) => chars[index] || '');
      }
    },
    { immediate: true }
  );

  // 监听验证码变化
  watch(fullCode, newCode => {
    emit('update:modelValue', newCode);
    emit('change', newCode);

    // 检查是否输入完成
    if (newCode.length === props.length) {
      emit('complete', newCode);
    }
  });

  // 获取输入框元素
  const getInputElement = (index: number): HTMLInputElement | null => {
    const inputRef = codeInputRefs.value[index];
    if (inputRef) {
      return inputRef.$el.querySelector('input');
    }
    return null;
  };

  // 聚焦到指定输入框
  const focusInput = (index: number) => {
    nextTick(() => {
      const inputEl = getInputElement(index);
      inputEl?.focus();
    });
  };

  // 处理验证码输入
  const handleCodeInput = (index: number, event: Event) => {
    if (props.disabled || props.readonly) return;

    const target = event.target as HTMLInputElement;
    let value = target.value;

    // 只允许数字，过滤非数字字符
    value = value.replace(/\D/g, '');

    // 如果输入多位数字，只取第一位
    if (value.length > 1) {
      value = value.charAt(0);
    }

    // 更新输入框值和数组
    target.value = value;
    codeArray.value[index] = value;

    // 自动跳到下一个输入框
    if (value && index < props.length - 1) {
      focusInput(index + 1);
    }
  };

  // 处理焦点事件
  const handleFocus = (index: number) => {
    // 选中当前输入框的内容
    nextTick(() => {
      const inputEl = getInputElement(index);
      inputEl?.select();
    });
  };

  // 处理粘贴事件
  const handlePaste = (startIndex: number, event: ClipboardEvent) => {
    if (props.disabled || props.readonly) return;

    event.preventDefault();
    const pasteData = event.clipboardData?.getData('text') || '';
    const digits = pasteData.replace(/\D/g, '');

    if (digits) {
      const charsToPaste = digits.split('');
      let pasteIndex = 0;
      while (pasteIndex < charsToPaste.length && startIndex + pasteIndex < props.length) {
        codeArray.value[startIndex + pasteIndex] = charsToPaste[pasteIndex];
        pasteIndex++;
      }

      const nextFocusIndex = Math.min(startIndex + pasteIndex, props.length - 1);
      focusInput(nextFocusIndex);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (index: number, event: KeyboardEvent) => {
    if (props.disabled || props.readonly) return;

    const currentValue = codeArray.value[index];

    // 处理退格键
    if (event.key === 'Backspace') {
      event.preventDefault();

      if (currentValue) {
        // 如果当前框有内容，清空当前框并跳到上一个框
        codeArray.value[index] = '';
        if (index > 0) {
          focusInput(index - 1);
        }
      } else if (index > 0) {
        // 如果当前框为空且不是第一个框，跳到前一个框并清空
        focusInput(index - 1);
        // 延迟清空前一个框的内容，确保焦点切换完成后再清空
        nextTick(() => {
          codeArray.value[index - 1] = '';
        });
      }
      return;
    }

    // 处理删除键
    if (event.key === 'Delete') {
      event.preventDefault();
      if (currentValue) {
        // 如果当前框有内容，清空当前框
        codeArray.value[index] = '';
      }
      return;
    }

    // 只允许数字键、退格键、删除键、箭头键等
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Home', 'End', 'Tab'];
    const isDigit = /^[0-9]$/.test(event.key);

    if (!isDigit && !allowedKeys.includes(event.key) && !event.metaKey && !event.ctrlKey) {
      event.preventDefault();
      return;
    }

    // 处理数字输入
    if (isDigit) {
      // 如果当前输入框已有内容，则覆盖
      event.preventDefault();
      codeArray.value[index] = event.key;

      // 自动跳到下一个输入框
      if (index < props.length - 1) {
        focusInput(index + 1);
      }
      return;
    }

    // 处理左右箭头键导航
    if (event.key === 'ArrowLeft' && index > 0) {
      event.preventDefault();
      focusInput(index - 1);
    }

    if (event.key === 'ArrowRight' && index < props.length - 1) {
      event.preventDefault();
      focusInput(index + 1);
    }

    // 处理Home和End键
    if (event.key === 'Home') {
      event.preventDefault();
      focusInput(0);
    }

    if (event.key === 'End') {
      event.preventDefault();
      focusInput(props.length - 1);
    }
  };

  // 清空验证码
  const clear = () => {
    codeArray.value = Array(props.length).fill('');
    focusInput(0);
  };

  // 聚焦到第一个输入框
  const focus = () => {
    focusInput(0);
  };

  return {
    codeArray,
    fullCode,
    setCodeInputRef,
    handleCodeInput,
    handlePaste,
    handleKeyDown,
    handleFocus,
    clear,
    focus
  };
}
