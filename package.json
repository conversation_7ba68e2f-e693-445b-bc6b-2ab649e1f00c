{"name": "pos", "private": true, "version": "3.0.0", "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"dev": "vite --mode development", "build": "vue-tsc --noEmit && vite build --mode production", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@vueuse/core": "^13.6.0", "axios": "^1.11.0", "dayjs": "^1.11.13", "frontend-tracking": "^1.2.3", "frontend-utils": "^0.2.18", "highlight.js": "^11.11.1", "lodash": "^4.17.21", "marked": "^16.1.2", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "secure-ls": "^2.0.0", "tdesign-icons-vue-next": "latest", "tdesign-vue-next": "latest", "vite-svg-loader": "^5.1.0", "vue": "^3.5.18", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.1", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.3.0", "eslint-plugin-vue-scoped-css": "^2.8.0", "globals": "^15.8.0", "husky": "^9.1.7", "is-ci": "^4.1.0", "less": "^4.4.0", "markdown-it": "^14.1.0", "typescript": "^5.8.3", "typescript-eslint": "^8.0.0", "unplugin-vue-markdown": "^29.1.0", "vite": "^7.0.6", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^3.0.4"}, "description": "pos web"}