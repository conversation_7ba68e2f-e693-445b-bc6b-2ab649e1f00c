.left-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--td-bg-color-container);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, .05);
    pointer-events: none;
    z-index: 1;
  }

  >* {
    position: relative;
    z-index: 2;
  }

  .app-version {
    position: absolute;
    bottom: 30px;
    left: 30px;
  }

  .customer-service {
    position: absolute;
    bottom: 30px;
    right: 30px;
  }
}

.right-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--td-bg-color-container);
  padding: 40px;
}