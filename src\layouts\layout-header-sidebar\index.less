.header-sidebar-layout {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .header {
    flex-shrink: 0;
    background-color: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l);
    min-height: 60px;
    display: flex;
    align-items: center;
  }

  .body {
    flex: 1;
    display: flex;
    overflow: hidden;

    .sidebar-left {
      max-width: var(--layout-sidebar-width);
      flex-shrink: 0;
      background-color: var(--td-bg-color-container);
      border-right: 1px solid var(--td-border-level-1-color);
      overflow-y: auto;
      position: relative;
    }

    .main-content {
      flex: 1;
      overflow-y: auto;
      background-color: var(--td-bg-color-container);
      position: relative;
      padding: var(--layout-spacing-medium);
    }
  }
}