@import './tdesign/index.less';

// 对部分样式进行重置
body {
  color: var(--td-text-color-secondary);
  font-family: -apple-system, BlinkMacSystemFont, var(--td-font-family);
  font: var(--td-font-body-medium);
  -webkit-font-smoothing: antialiased;
  padding: 0;
  margin: 0;
}

pre {
  font-family: var(--td-font-family);
}

ul,
dl,
li,
dd,
dt {
  margin: 0;
  padding: 0;
  list-style: none;
}

* {
  box-sizing: border-box;
}

.t-button-link,
a {
  color: var(--td-brand-color);
  text-decoration: none;
  cursor: pointer;

  &:hover {
    color: var(--td-brand-color-hover);
  }

  &:active {
    color: var(--td-brand-color-active);
  }

  &--active {
    color: var(--td-brand-color-active);
  }

  &:focus {
    text-decoration: none;
  }
}

.t-button-link {
  margin-right: var(--td-comp-margin-xxl);

  &:last-child {
    margin-right: 0;
  }
}

.t-button {
  margin: 2px;
  transition: none;

  &.t-size-l {
    height: 46px;
  }

  &.t-button--variant-base:not(.t-is-disabled) {
    border-width: 0;

    &.t-button--theme-primary {
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.12) inset, 0 -2px 0 0 rgba(10, 13, 18, 0.05) inset, 0 0 0 1px #1A5BD8;
    }

    &.t-button--theme-danger {
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.12) inset, 0 -2px 0 0 rgba(10, 13, 18, 0.05) inset, 0 0 0 1px #B83028;
    }
  }
}

.t-dialog__body {
  padding: 0;
}

.t-input__wrap {
  .t-input {
    transition: none;
    padding: var(--padding-p-12);
    box-sizing: content-box;
    height: unset;

    &.t-is-focused {
      font-weight: 500;

      &:not(.t-is-error) {
        box-shadow: 0 0 0 1px var(--td-brand-color) inset;
      }

      &.t-is-error {
        box-shadow: 0 0 0 1px var(--td-error-color) inset;
      }
    }
  }

  .t-input__tips {
    padding-top: var(--padding-p-8);

    &:not(.t-is-error) {
      color: var(--text-tertiary);
    }

    &.t-is-error {
      color: var(--text-error-primary);
    }
  }
}

