<template>
  <div class="doc-demo">
    <div class="doc-card">
      <h3>国际化 (i18n) 使用示例</h3>

      <!-- 语言切换 -->
      <div class="doc-section">
        <h4>语言切换：</h4>
        <div class="doc-item">
          <t-radio-group v-model="currentLocale" @change="handleLocaleChange">
            <t-radio value="zh_cn">中文</t-radio>
            <t-radio value="en">English</t-radio>
          </t-radio-group>
        </div>
      </div>

      <!-- 基础文本翻译示例 -->
      <div class="doc-section">
        <h4>基础文本翻译：</h4>
        <div class="doc-item">
          <span class="label">按钮文本：</span>
          <t-button>{{ $t('component.save') }}</t-button>
          <t-button theme="primary">{{ $t('component.action') }}</t-button>
          <t-button theme="default">{{ $t('component.cancel') }}</t-button>
        </div>
      </div>

      <!-- 错误消息示例 -->
      <div class="doc-section">
        <h4>错误消息示例：</h4>
        <div class="doc-item">
          <t-button @click="showErrorMessage(400)">{{ $t('message.error400') }}</t-button>
          <t-button @click="showErrorMessage(404)">{{ $t('message.error404') }}</t-button>
          <t-button @click="showErrorMessage(500)">{{ $t('message.error500') }}</t-button>
        </div>
      </div>

      <!-- 客服信息示例 -->
      <div class="doc-section">
        <h4>客服信息示例：</h4>
        <div class="doc-item">
          <div class="doc-info">
            <h5>{{ $t('customerService.title') }}</h5>
            <p>{{ $t('customerService.description') }}</p>
            <p>{{ $t('customerService.phoneNumber') }}</p>
            <p>{{ $t('customerService.workTime') }}</p>
          </div>
        </div>
      </div>

      <!-- 代码示例 -->
      <div class="doc-section">
        <h4>代码示例：</h4>
        <markdown-viewer>
          <template #markdown>
            <markdown />
          </template>
        </markdown-viewer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { MessagePlugin } from 'tdesign-vue-next';
import { useConfigStore } from '@/store';
import { MarkdownViewer } from '@/components';
import markdown from './index.md';

defineOptions({
  name: 'LocaleDemo'
});

const { t, locale } = useI18n();
const configStore = useConfigStore();

const configLocale = computed(() => {
  return configStore.locale;
});

// 当前语言
const currentLocale = ref(configLocale);

// 显示错误消息
const showErrorMessage = (errorCode: number) => {
  const messageKey = `message.error${errorCode}`;
  MessagePlugin.error(t(messageKey));
};

// 语言切换处理
const handleLocaleChange = (value: string) => {
  locale.value = value;
  configStore.setLocale(value as 'zh_cn' | 'en');
  MessagePlugin.success(`语言已切换为: ${value === 'zh_cn' ? '中文' : 'English'}`);
};
</script>

<style lang="less" scoped>
@import '@/style/example.less';
</style>
